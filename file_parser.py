"""
File Upload and Parsing System for Test Case Generator
Handles .txt, .docx, and Excel file parsing and content extraction
"""

import os
import shutil
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
from docx import Document
import uuid


class FileParser:
    """Handles file upload and content parsing"""
    
    def __init__(self, upload_dir: str = "data/uploads"):
        self.upload_dir = upload_dir
        self.ensure_upload_directory()
        self.supported_extensions = {'.txt', '.docx', '.xlsx', '.xls'}
    
    def ensure_upload_directory(self):
        """Ensure upload directory exists"""
        if not os.path.exists(self.upload_dir):
            os.makedirs(self.upload_dir)
    
    def is_supported_file(self, filename: str) -> bool:
        """Check if file extension is supported"""
        _, ext = os.path.splitext(filename.lower())
        return ext in self.supported_extensions
    
    def save_uploaded_file(self, file_obj, original_name: str, target_directory: str = None) -> Tuple[str, str]:
        """
        Save uploaded file to upload directory or specified target directory

        Args:
            file_obj: Gradio file object or file path string
            original_name: Original filename
            target_directory: Optional target directory (defaults to self.upload_dir)

        Returns:
            Tuple of (saved_file_path, file_type)
        """
        if not self.is_supported_file(original_name):
            raise ValueError(f"Unsupported file type. Supported: {', '.join(self.supported_extensions)}")

        # Handle Gradio file object or direct path
        if hasattr(file_obj, 'name'):
            # Gradio file object
            source_path = file_obj.name
        else:
            # Direct file path string
            source_path = str(file_obj)

        # Use target directory if provided, otherwise use default upload directory
        upload_dir = target_directory if target_directory else self.upload_dir

        # Ensure target directory exists
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        # Generate unique filename to avoid conflicts
        file_id = str(uuid.uuid4())
        _, ext = os.path.splitext(original_name.lower())
        # Extract just the filename from the original name (remove any path)
        clean_filename = os.path.basename(original_name)
        new_filename = f"{file_id}_{clean_filename}"
        saved_path = os.path.join(upload_dir, new_filename)

        # Copy file to upload directory
        shutil.copy2(source_path, saved_path)

        return saved_path, ext
    
    def parse_txt_file(self, file_path: str) -> str:
        """Parse text file and extract content"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content.strip()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
                return content.strip()
            except Exception as e:
                raise ValueError(f"Error reading text file: {str(e)}")
    
    def parse_docx_file(self, file_path: str) -> str:
        """Parse DOCX file and extract text content"""
        try:
            doc = Document(file_path)
            content_parts = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text.strip())
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content_parts.append(" | ".join(row_text))
            
            return "\n".join(content_parts)
        except Exception as e:
            raise ValueError(f"Error reading DOCX file: {str(e)}")
    
    def parse_excel_file(self, file_path: str) -> str:
        """Parse Excel file and extract content from all sheets"""
        try:
            # Read all sheets
            excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            content_parts = []
            
            for sheet_name, df in excel_data.items():
                if not df.empty:
                    content_parts.append(f"=== Sheet: {sheet_name} ===")
                    
                    # Convert DataFrame to string representation
                    # Handle NaN values
                    df_clean = df.fillna('')
                    
                    # Add column headers
                    headers = " | ".join(str(col) for col in df_clean.columns)
                    content_parts.append(headers)
                    content_parts.append("-" * len(headers))
                    
                    # Add rows
                    for _, row in df_clean.iterrows():
                        row_text = " | ".join(str(val) for val in row.values)
                        if row_text.strip():
                            content_parts.append(row_text)
                    
                    content_parts.append("")  # Add blank line between sheets
            
            return "\n".join(content_parts)
        except Exception as e:
            raise ValueError(f"Error reading Excel file: {str(e)}")
    
    def parse_file(self, file_path: str, file_type: str) -> str:
        """
        Parse file based on its type and extract content
        
        Args:
            file_path: Path to the file
            file_type: File extension (e.g., '.txt', '.docx', '.xlsx')
            
        Returns:
            Extracted text content
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_type = file_type.lower()
        
        if file_type == '.txt':
            return self.parse_txt_file(file_path)
        elif file_type == '.docx':
            return self.parse_docx_file(file_path)
        elif file_type in ['.xlsx', '.xls']:
            return self.parse_excel_file(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
    
    def extract_requirements_info(self, content: str) -> Dict[str, Any]:
        """
        Extract structured information from parsed content
        Enhanced for better file type detection and content analysis
        """
        info = {
            'raw_content': content,
            'functions': [],
            'requirements': [],
            'validations': [],
            'business_flows': [],
            'file_type': 'unknown',
            'content_summary': '',
            'key_entities': [],
            'apis': [],
            'ui_elements': []
        }

        content_lower = content.lower()
        lines = content.split('\n')
        current_section = None

        # Enhanced file type detection
        info['file_type'] = self._detect_file_type(content_lower)

        # Extract key entities and components
        info['key_entities'] = self._extract_key_entities(content)
        info['apis'] = self._extract_api_endpoints(content)
        info['ui_elements'] = self._extract_ui_elements(content)

        # Generate content summary
        info['content_summary'] = self._generate_content_summary(content, info['file_type'])

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Identify sections (enhanced pattern matching)
            line_lower = line.lower()

            if any(keyword in line_lower for keyword in ['chức năng', 'function', 'feature', 'tính năng']):
                current_section = 'functions'
                info['functions'].append(line)
            elif any(keyword in line_lower for keyword in ['yêu cầu', 'requirement', 'spec', 'specification']):
                current_section = 'requirements'
                info['requirements'].append(line)
            elif any(keyword in line_lower for keyword in ['validation', 'validate', 'kiểm tra', 'xác thực']):
                current_section = 'validations'
                info['validations'].append(line)
            elif any(keyword in line_lower for keyword in ['luồng', 'flow', 'workflow', 'quy trình']):
                current_section = 'business_flows'
                info['business_flows'].append(line)
            elif current_section:
                # Add to current section
                info[current_section].append(line)

        return info

    def _detect_file_type(self, content_lower: str) -> str:
        """Detect the type of requirements file based on content"""
        # API specification indicators
        api_keywords = ['api', 'endpoint', 'request', 'response', 'http', 'get', 'post', 'put', 'delete', 'rest']
        if any(keyword in content_lower for keyword in api_keywords):
            return 'api_specification'

        # UI/UX requirements indicators
        ui_keywords = ['ui', 'interface', 'screen', 'form', 'button', 'giao diện', 'màn hình', 'nút']
        if any(keyword in content_lower for keyword in ui_keywords):
            return 'ui_requirements'

        # Business logic indicators
        business_keywords = ['business', 'workflow', 'process', 'rule', 'logic', 'quy trình', 'nghiệp vụ']
        if any(keyword in content_lower for keyword in business_keywords):
            return 'business_logic'

        # Database/Data model indicators
        data_keywords = ['database', 'table', 'schema', 'model', 'entity', 'cơ sở dữ liệu', 'bảng']
        if any(keyword in content_lower for keyword in data_keywords):
            return 'data_model'

        # Security requirements indicators
        security_keywords = ['security', 'authentication', 'authorization', 'permission', 'bảo mật', 'xác thực']
        if any(keyword in content_lower for keyword in security_keywords):
            return 'security_requirements'

        return 'general_requirements'

    def _extract_key_entities(self, content: str) -> List[str]:
        """Extract key business entities from content"""
        import re
        entities = set()

        # Common entity patterns
        entity_patterns = [
            r'(?:user|người dùng|khách hàng)\s*(?:management|quản lý)?',
            r'(?:product|sản phẩm)\s*(?:management|quản lý)?',
            r'(?:order|đơn hàng)\s*(?:management|quản lý)?',
            r'(?:customer|khách hàng)\s*(?:management|quản lý)?',
            r'(?:report|báo cáo)\s*(?:management|quản lý)?',
            r'(?:invoice|hóa đơn)\s*(?:management|quản lý)?',
            r'(?:payment|thanh toán)\s*(?:management|quản lý)?'
        ]

        for pattern in entity_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            entities.update(matches)

        return list(entities)[:10]  # Limit to top 10

    def _extract_api_endpoints(self, content: str) -> List[str]:
        """Extract API endpoints from content"""
        import re
        endpoints = []

        # API endpoint patterns
        endpoint_patterns = [
            r'(GET|POST|PUT|DELETE)\s+(/[^\s]+)',
            r'endpoint\s*:\s*([^\s]+)',
            r'api\s*:\s*([^\s]+)',
            r'/api/[^\s]+'
        ]

        for pattern in endpoint_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    endpoints.append(' '.join(match))
                else:
                    endpoints.append(match)

        return endpoints[:20]  # Limit to top 20

    def _extract_ui_elements(self, content: str) -> List[str]:
        """Extract UI elements from content"""
        import re
        ui_elements = set()

        # UI element patterns
        ui_patterns = [
            r'(?:button|nút)\s*[:\-]?\s*([^\n]+)',
            r'(?:form|biểu mẫu)\s*[:\-]?\s*([^\n]+)',
            r'(?:screen|màn hình)\s*[:\-]?\s*([^\n]+)',
            r'(?:field|trường)\s*[:\-]?\s*([^\n]+)',
            r'(?:menu|thực đơn)\s*[:\-]?\s*([^\n]+)'
        ]

        for pattern in ui_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            ui_elements.update([match.strip() for match in matches if match.strip()])

        return list(ui_elements)[:15]  # Limit to top 15

    def _generate_content_summary(self, content: str, file_type: str) -> str:
        """Generate a brief summary of the content"""
        lines = [line.strip() for line in content.split('\n') if line.strip()]

        if not lines:
            return "Empty content"

        # Take first few meaningful lines as summary
        summary_lines = []
        for line in lines[:5]:
            if len(line) > 10:  # Skip very short lines
                summary_lines.append(line)
                if len(summary_lines) >= 3:
                    break

        summary = ' | '.join(summary_lines)
        return summary[:200] + '...' if len(summary) > 200 else summary
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get basic file information"""
        if not os.path.exists(file_path):
            return {}
        
        stat = os.stat(file_path)
        return {
            'size': stat.st_size,
            'modified': stat.st_mtime,
            'exists': True
        }
