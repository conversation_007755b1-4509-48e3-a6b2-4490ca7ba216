#!/usr/bin/env python3
"""
YouTube Audio Downloader
Tải audio từ link YouTube với chất lượng cao
"""

import os
import sys
import re
from pathlib import Path
import argparse
from typing import Optional, Dict, Any

try:
    import yt_dlp
except ImportError:
    print("Cần cài đặt yt-dlp: pip install yt-dlp")
    sys.exit(1)


class YouTubeAudioDownloader:
    """Class để tải audio từ YouTube"""
    
    def __init__(self, output_dir: str = "downloads"):
        """
        Khởi tạo downloader
        
        Args:
            output_dir: Thư mục lưu file audio (mặc định: downloads)
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Cấu hình yt-dlp
        self.ydl_opts = {
            'format': 'bestaudio/best',  # Chọn audio chất lượng tốt nhất
            'outtmpl': str(self.output_dir / '%(title)s.%(ext)s'),
            'extractaudio': True,
            'audioformat': 'mp3',
            'audioquality': '192',  # Chất lượng audio 192kbps
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
            'noplaylist': True,  # Chỉ tải video đơn lẻ, không tải playlist
        }
    
    def is_valid_youtube_url(self, url: str) -> bool:
        """
        Kiểm tra URL YouTube có hợp lệ không
        
        Args:
            url: URL cần kiểm tra
            
        Returns:
            True nếu URL hợp lệ, False nếu không
        """
        youtube_patterns = [
            r'https?://(?:www\.)?youtube\.com/watch\?v=[\w-]+',
            r'https?://(?:www\.)?youtu\.be/[\w-]+',
            r'https?://(?:www\.)?youtube\.com/embed/[\w-]+',
            r'https?://(?:www\.)?youtube\.com/v/[\w-]+',
        ]
        
        return any(re.match(pattern, url) for pattern in youtube_patterns)
    
    def get_video_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Lấy thông tin video từ URL
        
        Args:
            url: URL YouTube
            
        Returns:
            Dictionary chứa thông tin video hoặc None nếu lỗi
        """
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                return {
                    'title': info.get('title', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'view_count': info.get('view_count', 0),
                    'upload_date': info.get('upload_date', 'Unknown'),
                }
        except Exception as e:
            print(f"Lỗi khi lấy thông tin video: {e}")
            return None
    
    def download_audio(self, url: str, custom_filename: Optional[str] = None) -> bool:
        """
        Tải audio từ YouTube URL
        
        Args:
            url: URL YouTube
            custom_filename: Tên file tùy chỉnh (không bao gồm extension)
            
        Returns:
            True nếu tải thành công, False nếu thất bại
        """
        if not self.is_valid_youtube_url(url):
            print("URL YouTube không hợp lệ!")
            return False
        
        # Cập nhật tên file nếu có tùy chỉnh
        opts = self.ydl_opts.copy()
        if custom_filename:
            # Làm sạch tên file
            clean_filename = re.sub(r'[<>:"/\\|?*]', '_', custom_filename)
            opts['outtmpl'] = str(self.output_dir / f'{clean_filename}.%(ext)s')
        
        try:
            print(f"Đang tải audio từ: {url}")
            
            # Lấy thông tin video trước
            info = self.get_video_info(url)
            if info:
                print(f"Tiêu đề: {info['title']}")
                print(f"Kênh: {info['uploader']}")
                duration_min = info['duration'] // 60
                duration_sec = info['duration'] % 60
                print(f"Thời lượng: {duration_min}:{duration_sec:02d}")
            
            # Tải audio
            with yt_dlp.YoutubeDL(opts) as ydl:
                ydl.download([url])
            
            print(f"✅ Tải thành công! File được lưu trong thư mục: {self.output_dir}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi tải audio: {e}")
            return False
    
    def download_multiple(self, urls: list, custom_filenames: Optional[list] = None) -> Dict[str, bool]:
        """
        Tải nhiều audio từ danh sách URLs
        
        Args:
            urls: Danh sách URLs YouTube
            custom_filenames: Danh sách tên file tùy chỉnh (tùy chọn)
            
        Returns:
            Dictionary với URL làm key và kết quả (True/False) làm value
        """
        results = {}
        
        for i, url in enumerate(urls):
            print(f"\n--- Tải file {i+1}/{len(urls)} ---")
            
            filename = None
            if custom_filenames and i < len(custom_filenames):
                filename = custom_filenames[i]
            
            results[url] = self.download_audio(url, filename)
        
        return results


def main():
    """Hàm main để chạy từ command line"""
    parser = argparse.ArgumentParser(description='Tải audio từ YouTube')
    parser.add_argument('url', help='URL YouTube')
    parser.add_argument('-o', '--output', default='downloads', 
                       help='Thư mục output (mặc định: downloads)')
    parser.add_argument('-n', '--name', help='Tên file tùy chỉnh')
    
    args = parser.parse_args()
    
    # Tạo downloader
    downloader = YouTubeAudioDownloader(args.output)
    
    # Tải audio
    success = downloader.download_audio(args.url, args.name)
    
    if success:
        print("\n🎵 Hoàn thành!")
    else:
        print("\n❌ Tải thất bại!")
        sys.exit(1)


if __name__ == "__main__":
    # Ví dụ sử dụng
    if len(sys.argv) == 1:
        print("=== YouTube Audio Downloader ===")
        print("\nCách sử dụng:")
        print("1. Từ command line:")
        print("   python youtube_audio_downloader.py 'https://www.youtube.com/watch?v=VIDEO_ID'")
        print("   python youtube_audio_downloader.py 'URL' -o 'thư_mục' -n 'tên_file'")
        print("\n2. Trong code Python:")
        print("   downloader = YouTubeAudioDownloader('downloads')")
        print("   downloader.download_audio('https://www.youtube.com/watch?v=VIDEO_ID')")
        
        # Demo tương tác
        print("\n--- Demo tương tác ---")
        url = input("Nhập URL YouTube (hoặc Enter để thoát): ").strip()
        
        if url:
            downloader = YouTubeAudioDownloader()
            downloader.download_audio(url)
    else:
        main()
