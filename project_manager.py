"""
Project Management System for Test Case Generator
Handles project creation, storage, and file management
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import uuid
import stat


class ProjectManager:
    """Manages projects and their associated files"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.projects_file = os.path.join(data_dir, "projects.json")
        self.projects_dir = os.path.join(data_dir, "projects")
        self.ensure_data_directory()
        self.projects = self.load_projects()
    
    def ensure_data_directory(self):
        """Ensure data directory exists"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        if not os.path.exists(self.projects_dir):
            os.makedirs(self.projects_dir)
    
    def load_projects(self) -> Dict[str, Any]:
        """Load projects from JSON file"""
        if os.path.exists(self.projects_file):
            try:
                with open(self.projects_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def save_projects(self):
        """Save projects to JSON file"""
        with open(self.projects_file, 'w', encoding='utf-8') as f:
            json.dump(self.projects, f, ensure_ascii=False, indent=2)
    
    def create_project(self, name: str, description: str = "") -> str:
        """
        Create a new project
        
        Args:
            name: Project name
            description: Project description
            
        Returns:
            project_id: Unique project identifier
        """
        if not name.strip():
            raise ValueError("Project name cannot be empty")
        
        # Check if project name already exists
        for project_id, project in self.projects.items():
            if project['name'].lower() == name.lower():
                raise ValueError(f"Project '{name}' already exists")
        
        project_id = str(uuid.uuid4())
        project_data = {
            'id': project_id,
            'name': name.strip(),
            'description': description.strip(),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'files': [],
            'generated_outputs': []
        }

        # Create project-specific directories
        self.create_project_directories(project_id, name.strip())

        self.projects[project_id] = project_data
        self.save_projects()

        return project_id

    def create_project_directories(self, project_id: str, project_name: str):
        """Create project-specific directories"""
        # Sanitize project name for directory creation
        safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')

        project_path = os.path.join(self.projects_dir, f"{safe_name}_{project_id[:8]}")
        inputs_path = os.path.join(project_path, "inputs")
        outputs_path = os.path.join(project_path, "outputs")

        os.makedirs(inputs_path, exist_ok=True)
        os.makedirs(outputs_path, exist_ok=True)

        return project_path

    def get_project_path(self, project_id: str) -> str:
        """Get the file system path for a project"""
        project = self.get_project(project_id)
        if not project:
            return ""

        safe_name = "".join(c for c in project['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')

        return os.path.join(self.projects_dir, f"{safe_name}_{project_id[:8]}")

    def get_project_inputs_path(self, project_id: str) -> str:
        """Get the inputs directory path for a project"""
        project_path = self.get_project_path(project_id)
        return os.path.join(project_path, "inputs") if project_path else ""

    def get_project_outputs_path(self, project_id: str) -> str:
        """Get the outputs directory path for a project"""
        project_path = self.get_project_path(project_id)
        return os.path.join(project_path, "outputs") if project_path else ""

    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get project by ID"""
        return self.projects.get(project_id)
    
    def get_project_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get project by name"""
        for project in self.projects.values():
            if project['name'].lower() == name.lower():
                return project
        return None
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """List all projects"""
        return list(self.projects.values())
    
    def get_project_names(self) -> List[str]:
        """Get list of project names"""
        return [project['name'] for project in self.projects.values()]
    
    def update_project(self, project_id: str, name: str = None, description: str = None):
        """Update project details"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        project = self.projects[project_id]
        
        if name is not None:
            # Check if new name conflicts with existing projects
            for pid, p in self.projects.items():
                if pid != project_id and p['name'].lower() == name.lower():
                    raise ValueError(f"Project '{name}' already exists")
            project['name'] = name.strip()
        
        if description is not None:
            project['description'] = description.strip()
        
        project['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def delete_project(self, project_id: str):
        """Delete a project and its associated files"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        # Clean up associated files
        project = self.projects[project_id]
        for file_info in project.get('files', []):
            file_path = file_info.get('path')
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except OSError:
                    pass  # File might be in use or already deleted
        
        # Clean up generated outputs
        for output_info in project.get('generated_outputs', []):
            for file_path in [output_info.get('txt_path'), output_info.get('excel_path')]:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except OSError:
                        pass
        
        del self.projects[project_id]
        self.save_projects()
    
    def add_file_to_project(self, project_id: str, file_path: str, original_name: str, file_type: str, file_metadata: Dict[str, Any] = None):
        """Add a file to a project with enhanced metadata"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        # Get file size
        file_size = 0
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)

        file_info = {
            'id': str(uuid.uuid4()),
            'path': file_path,
            'original_name': original_name,
            'file_type': file_type,
            'file_size': file_size,
            'uploaded_at': datetime.now().isoformat(),
            'processing_status': 'pending',
            'category': 'input',  # input or output
            'metadata': file_metadata or {}
        }

        self.projects[project_id]['files'].append(file_info)
        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()

        # Analyze file relationships after adding new file
        self._analyze_project_file_relationships(project_id)
    
    def add_generated_output(self, project_id: str, txt_path: str, excel_path: str, model_used: str = "Mock"):
        """Add generated output files to a project"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        # Get file sizes
        txt_size = os.path.getsize(txt_path) if os.path.exists(txt_path) else 0
        excel_size = os.path.getsize(excel_path) if os.path.exists(excel_path) else 0

        # Convert absolute paths to relative paths within project structure
        project_path = self.get_project_path(project_id)

        # Store relative paths if files are within project directory
        if project_path and txt_path.startswith(project_path):
            txt_relative_path = os.path.relpath(txt_path, project_path)
        else:
            txt_relative_path = txt_path

        if project_path and excel_path.startswith(project_path):
            excel_relative_path = os.path.relpath(excel_path, project_path)
        else:
            excel_relative_path = excel_path

        # Add TXT file info
        txt_file_info = {
            'id': str(uuid.uuid4()),
            'path': txt_path,  # Keep absolute path for backward compatibility
            'relative_path': txt_relative_path,  # Add relative path for better organization
            'original_name': os.path.basename(txt_path),
            'file_type': '.txt',
            'file_size': txt_size,
            'uploaded_at': datetime.now().isoformat(),
            'processing_status': 'processed',
            'category': 'output',
            'model_used': model_used
        }

        # Add Excel file info
        excel_file_info = {
            'id': str(uuid.uuid4()),
            'path': excel_path,  # Keep absolute path for backward compatibility
            'relative_path': excel_relative_path,  # Add relative path for better organization
            'original_name': os.path.basename(excel_path),
            'file_type': '.xlsx',
            'file_size': excel_size,
            'uploaded_at': datetime.now().isoformat(),
            'processing_status': 'processed',
            'category': 'output',
            'model_used': model_used
        }

        # Add to files list
        self.projects[project_id]['files'].extend([txt_file_info, excel_file_info])

        # Keep legacy generated_outputs for backward compatibility
        output_info = {
            'txt_path': txt_path,
            'excel_path': excel_path,
            'generated_at': datetime.now().isoformat(),
            'model_used': model_used
        }

        # Only add to generated_outputs if not already present (avoid duplicates)
        existing_outputs = self.projects[project_id].get('generated_outputs', [])
        if not any(output['txt_path'] == txt_path and output['excel_path'] == excel_path
                  for output in existing_outputs):
            self.projects[project_id]['generated_outputs'].append(output_info)

        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def get_project_files(self, project_id: str) -> List[Dict[str, Any]]:
        """Get all files associated with a project"""
        project = self.get_project(project_id)
        if not project:
            return []
        return project.get('files', [])

    def get_project_files_by_category(self, project_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Get project files organized by category (input/output)"""
        files = self.get_project_files(project_id)
        categorized = {'input': [], 'output': []}

        for file_info in files:
            category = file_info.get('category', 'input')
            categorized[category].append(file_info)

        return categorized

    def update_file_processing_status(self, project_id: str, file_id: str, status: str):
        """Update the processing status of a file"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        for file_info in self.projects[project_id]['files']:
            if file_info.get('id') == file_id:
                file_info['processing_status'] = status
                file_info['updated_at'] = datetime.now().isoformat()
                break

        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()

    def get_folder_structure(self, project_id: str) -> Dict[str, Any]:
        """Get hierarchical folder structure for a project"""
        project_path = self.get_project_path(project_id)
        if not project_path or not os.path.exists(project_path):
            return {}

        def scan_directory(path: str, name: str = None) -> Dict[str, Any]:
            """Recursively scan directory structure"""
            if not os.path.exists(path):
                return {}

            dir_info = {
                'name': name or os.path.basename(path),
                'type': 'directory',
                'path': path,
                'size': 0,
                'file_count': 0,
                'folder_count': 0,
                'created': None,
                'modified': None,
                'children': []
            }

            try:
                # Get directory metadata
                dir_stat = os.stat(path)
                dir_info['created'] = datetime.fromtimestamp(dir_stat.st_ctime).isoformat()
                dir_info['modified'] = datetime.fromtimestamp(dir_stat.st_mtime).isoformat()

                # Scan directory contents
                items = []
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)

                    if os.path.isdir(item_path):
                        # Recursively scan subdirectory
                        subdir_info = scan_directory(item_path, item)
                        items.append(subdir_info)
                        dir_info['folder_count'] += 1 + subdir_info['folder_count']
                        dir_info['file_count'] += subdir_info['file_count']
                        dir_info['size'] += subdir_info['size']
                    else:
                        # Add file information
                        try:
                            file_stat = os.stat(item_path)
                            file_info = {
                                'name': item,
                                'type': 'file',
                                'path': item_path,
                                'size': file_stat.st_size,
                                'created': datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                                'modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                                'extension': os.path.splitext(item)[1].lower()
                            }
                            items.append(file_info)
                            dir_info['file_count'] += 1
                            dir_info['size'] += file_stat.st_size
                        except (OSError, IOError):
                            continue

                # Sort items: directories first, then files, both alphabetically
                items.sort(key=lambda x: (x['type'] == 'file', x['name'].lower()))
                dir_info['children'] = items

            except (OSError, IOError):
                pass

            return dir_info

        return scan_directory(project_path, f"Project: {self.get_project(project_id)['name']}")

    def get_folder_tree_display(self, project_id: str) -> str:
        """Generate a text-based tree display of project folder structure"""
        structure = self.get_folder_structure(project_id)
        if not structure:
            return "📁 Project folder not found or empty"

        def format_tree(item: Dict[str, Any], prefix: str = "", is_last: bool = True) -> List[str]:
            """Format directory tree with proper indentation"""
            lines = []

            # Choose appropriate icon and connector
            if item['type'] == 'directory':
                icon = "📁"
                connector = "└── " if is_last else "├── "
            else:
                icon = self._get_file_icon(item.get('extension', ''))
                connector = "└── " if is_last else "├── "

            # Format size
            size_str = self.format_file_size(item['size'])

            # Create the line
            if item['type'] == 'directory':
                count_info = f"({item['file_count']} files, {item['folder_count']} folders)"
                line = f"{prefix}{connector}{icon} **{item['name']}** {count_info} - {size_str}"
            else:
                line = f"{prefix}{connector}{icon} {item['name']} - {size_str}"

            lines.append(line)

            # Add children for directories
            if item['type'] == 'directory' and item.get('children'):
                child_prefix = prefix + ("    " if is_last else "│   ")
                for i, child in enumerate(item['children']):
                    is_last_child = i == len(item['children']) - 1
                    lines.extend(format_tree(child, child_prefix, is_last_child))

            return lines

        tree_lines = format_tree(structure)
        return "\n".join(tree_lines)

    def _get_file_icon(self, extension: str) -> str:
        """Get appropriate icon for file type"""
        icon_map = {
            '.txt': '📄',
            '.docx': '📝',
            '.doc': '📝',
            '.xlsx': '📊',
            '.xls': '📊',
            '.csv': '📋',
            '.pdf': '📕',
            '.json': '⚙️',
            '.xml': '⚙️',
            '.log': '📜'
        }
        return icon_map.get(extension.lower(), '📄')

    def get_detailed_file_statistics(self, project_id: str) -> Dict[str, Any]:
        """Get comprehensive file statistics and metadata for a project"""
        structure = self.get_folder_structure(project_id)
        if not structure:
            return {}

        stats = {
            'total_files': 0,
            'total_folders': 0,
            'total_size': 0,
            'file_types': {},
            'size_distribution': {
                'small': 0,    # < 1MB
                'medium': 0,   # 1MB - 10MB
                'large': 0     # > 10MB
            },
            'recent_files': [],
            'largest_files': [],
            'oldest_files': [],
            'newest_files': []
        }

        all_files = []

        def collect_files(item: Dict[str, Any]):
            """Recursively collect all files"""
            if item['type'] == 'file':
                all_files.append(item)
                stats['total_files'] += 1
                stats['total_size'] += item['size']

                # Count by file type
                ext = item.get('extension', '').lower()
                if ext:
                    stats['file_types'][ext] = stats['file_types'].get(ext, 0) + 1
                else:
                    stats['file_types']['no_extension'] = stats['file_types'].get('no_extension', 0) + 1

                # Size distribution
                size_mb = item['size'] / (1024 * 1024)
                if size_mb < 1:
                    stats['size_distribution']['small'] += 1
                elif size_mb <= 10:
                    stats['size_distribution']['medium'] += 1
                else:
                    stats['size_distribution']['large'] += 1

            elif item['type'] == 'directory':
                stats['total_folders'] += 1
                for child in item.get('children', []):
                    collect_files(child)

        collect_files(structure)

        # Sort files for various categories
        if all_files:
            # Sort by modification time (newest first)
            all_files.sort(key=lambda x: x['modified'], reverse=True)
            stats['newest_files'] = all_files[:5]
            stats['oldest_files'] = all_files[-5:]

            # Sort by size (largest first)
            all_files.sort(key=lambda x: x['size'], reverse=True)
            stats['largest_files'] = all_files[:5]

            # Recent files (modified in last 7 days)
            from datetime import datetime, timedelta
            week_ago = datetime.now() - timedelta(days=7)
            stats['recent_files'] = [
                f for f in all_files
                if datetime.fromisoformat(f['modified']) > week_ago
            ][:10]

        return stats

    def get_file_type_summary(self, project_id: str) -> Dict[str, Any]:
        """Get summary of file types in the project"""
        stats = self.get_detailed_file_statistics(project_id)
        file_types = stats.get('file_types', {})

        summary = {
            'total_types': len(file_types),
            'types': []
        }

        # Sort by count (descending)
        sorted_types = sorted(file_types.items(), key=lambda x: x[1], reverse=True)

        for ext, count in sorted_types:
            type_info = {
                'extension': ext,
                'count': count,
                'icon': self._get_file_icon(ext),
                'description': self._get_file_type_description(ext)
            }
            summary['types'].append(type_info)

        return summary

    def _get_file_type_description(self, extension: str) -> str:
        """Get human-readable description for file type"""
        descriptions = {
            '.txt': 'Text Document',
            '.docx': 'Word Document',
            '.doc': 'Word Document (Legacy)',
            '.xlsx': 'Excel Spreadsheet',
            '.xls': 'Excel Spreadsheet (Legacy)',
            '.csv': 'CSV Data File',
            '.pdf': 'PDF Document',
            '.json': 'JSON Configuration',
            '.xml': 'XML Document',
            '.log': 'Log File',
            'no_extension': 'File (No Extension)'
        }
        return descriptions.get(extension.lower(), 'Unknown File Type')

    def get_project_timeline(self, project_id: str) -> Dict[str, Any]:
        """Get timeline of project activity"""
        project = self.get_project(project_id)
        if not project:
            return {}

        timeline = {
            'project_created': project.get('created_at'),
            'project_updated': project.get('updated_at'),
            'file_activities': []
        }

        # Add file upload activities
        for file_info in project.get('files', []):
            activity = {
                'type': 'file_upload',
                'timestamp': file_info.get('uploaded_at'),
                'description': f"Uploaded {file_info.get('original_name', 'Unknown file')}",
                'file_name': file_info.get('original_name'),
                'file_size': file_info.get('file_size', 0)
            }
            timeline['file_activities'].append(activity)

        # Sort activities by timestamp (newest first)
        timeline['file_activities'].sort(
            key=lambda x: x['timestamp'] or '',
            reverse=True
        )

        return timeline

    def get_comprehensive_project_info(self, project_id: str) -> Dict[str, Any]:
        """Get comprehensive project information including metadata and statistics"""
        project = self.get_project(project_id)
        if not project:
            return {}

        # Get basic project info
        info = {
            'basic': {
                'id': project_id,
                'name': project['name'],
                'description': project.get('description', ''),
                'created_at': project.get('created_at'),
                'updated_at': project.get('updated_at'),
                'project_path': self.get_project_path(project_id)
            },
            'statistics': self.get_detailed_file_statistics(project_id),
            'file_types': self.get_file_type_summary(project_id),
            'timeline': self.get_project_timeline(project_id),
            'folder_structure': self.get_folder_structure(project_id)
        }

        # Calculate project age
        if project.get('created_at'):
            try:
                created = datetime.fromisoformat(project['created_at'])
                age_days = (datetime.now() - created).days
                info['basic']['age_days'] = age_days
                info['basic']['age_description'] = self._format_age(age_days)
            except ValueError:
                info['basic']['age_days'] = 0
                info['basic']['age_description'] = 'Unknown'

        return info

    def optimize_project_storage(self, project_id: str = None):
        """
        Optimize project storage by removing redundancy and standardizing paths

        Args:
            project_id: Specific project to optimize, or None to optimize all projects
        """
        projects_to_optimize = [project_id] if project_id else list(self.projects.keys())

        for pid in projects_to_optimize:
            if pid not in self.projects:
                continue

            project = self.projects[pid]

            # Remove duplicate entries in generated_outputs
            if 'generated_outputs' in project:
                seen_outputs = set()
                unique_outputs = []

                for output in project['generated_outputs']:
                    output_key = (output.get('txt_path', ''), output.get('excel_path', ''))
                    if output_key not in seen_outputs:
                        seen_outputs.add(output_key)
                        unique_outputs.append(output)

                project['generated_outputs'] = unique_outputs

            # Standardize file paths to use relative paths where possible
            project_path = self.get_project_path(pid)
            if project_path and 'files' in project:
                for file_info in project['files']:
                    if 'path' in file_info and file_info['path'].startswith(project_path):
                        if 'relative_path' not in file_info:
                            file_info['relative_path'] = os.path.relpath(file_info['path'], project_path)

            project['updated_at'] = datetime.now().isoformat()

        self.save_projects()

    def get_project_storage_stats(self) -> Dict[str, Any]:
        """Get statistics about project storage efficiency"""
        total_projects = len(self.projects)
        total_files = sum(len(project.get('files', [])) for project in self.projects.values())
        total_outputs = sum(len(project.get('generated_outputs', [])) for project in self.projects.values())

        # Calculate file size of projects.json
        projects_file_size = 0
        if os.path.exists(self.projects_file):
            projects_file_size = os.path.getsize(self.projects_file)

        return {
            'total_projects': total_projects,
            'total_files': total_files,
            'total_outputs': total_outputs,
            'projects_file_size_bytes': projects_file_size,
            'projects_file_size_mb': round(projects_file_size / (1024 * 1024), 2)
        }

    def _format_age(self, days: int) -> str:
        """Format project age in human-readable format"""
        if days == 0:
            return "Created today"
        elif days == 1:
            return "1 day old"
        elif days < 7:
            return f"{days} days old"
        elif days < 30:
            weeks = days // 7
            return f"{weeks} week{'s' if weeks > 1 else ''} old"
        elif days < 365:
            months = days // 30
            return f"{months} month{'s' if months > 1 else ''} old"
        else:
            years = days // 365
            return f"{years} year{'s' if years > 1 else ''} old"

    def format_project_info_display(self, project_id: str) -> str:
        """Format comprehensive project information for display"""
        info = self.get_comprehensive_project_info(project_id)
        if not info:
            return "❌ Project not found"

        basic = info['basic']
        stats = info['statistics']
        file_types = info['file_types']

        lines = []

        # Project header
        lines.append(f"📋 **Project Information: {basic['name']}**")
        lines.append("")

        # Basic information
        lines.append("🔍 **Basic Details**")
        lines.append(f"• **Name:** {basic['name']}")
        if basic.get('description'):
            lines.append(f"• **Description:** {basic['description']}")
        lines.append(f"• **Created:** {self._format_datetime(basic.get('created_at'))}")
        lines.append(f"• **Last Modified:** {self._format_datetime(basic.get('updated_at'))}")
        lines.append(f"• **Age:** {basic.get('age_description', 'Unknown')}")
        lines.append(f"• **Project ID:** `{basic['id'][:8]}...`")
        lines.append("")

        # Statistics
        lines.append("📊 **Project Statistics**")
        lines.append(f"• **Total Files:** {stats.get('total_files', 0)}")
        lines.append(f"• **Total Folders:** {stats.get('total_folders', 0)}")
        lines.append(f"• **Total Size:** {self.format_file_size(stats.get('total_size', 0))}")
        lines.append(f"• **File Types:** {file_types.get('total_types', 0)} different types")
        lines.append("")

        # File type breakdown
        if file_types.get('types'):
            lines.append("📁 **File Type Breakdown**")
            for type_info in file_types['types'][:5]:  # Show top 5 types
                ext = type_info['extension']
                count = type_info['count']
                icon = type_info['icon']
                desc = type_info['description']
                lines.append(f"• {icon} **{desc}:** {count} file{'s' if count > 1 else ''}")

            if len(file_types['types']) > 5:
                lines.append(f"• ... and {len(file_types['types']) - 5} more types")
            lines.append("")

        # Size distribution
        size_dist = stats.get('size_distribution', {})
        if any(size_dist.values()):
            lines.append("📏 **File Size Distribution**")
            lines.append(f"• **Small files** (< 1MB): {size_dist.get('small', 0)}")
            lines.append(f"• **Medium files** (1-10MB): {size_dist.get('medium', 0)}")
            lines.append(f"• **Large files** (> 10MB): {size_dist.get('large', 0)}")
            lines.append("")

        # Recent activity
        recent_files = stats.get('recent_files', [])
        if recent_files:
            lines.append("🕒 **Recent Activity**")
            for file_info in recent_files[:3]:  # Show 3 most recent
                name = file_info['name']
                modified = self._format_datetime(file_info['modified'])
                size = self.format_file_size(file_info['size'])
                lines.append(f"• **{name}** - {size} (modified {modified})")
            lines.append("")

        return "\n".join(lines)

    def _format_datetime(self, iso_string: str) -> str:
        """Format ISO datetime string for display"""
        if not iso_string:
            return "Unknown"

        try:
            dt = datetime.fromisoformat(iso_string)
            now = datetime.now()
            diff = now - dt

            if diff.days == 0:
                if diff.seconds < 3600:  # Less than 1 hour
                    minutes = diff.seconds // 60
                    return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
                else:  # Less than 1 day
                    hours = diff.seconds // 3600
                    return f"{hours} hour{'s' if hours != 1 else ''} ago"
            elif diff.days == 1:
                return "Yesterday"
            elif diff.days < 7:
                return f"{diff.days} days ago"
            else:
                return dt.strftime("%Y-%m-%d %H:%M")
        except ValueError:
            return iso_string

    def get_folder_navigation_info(self, project_id: str, current_path: str = None) -> Dict[str, Any]:
        """Get navigation information for browsing project folders"""
        project_path = self.get_project_path(project_id)
        if not project_path or not os.path.exists(project_path):
            return {}

        # Use project root if no current path specified
        if not current_path:
            current_path = project_path

        # Ensure current path is within project directory
        if not current_path.startswith(project_path):
            current_path = project_path

        nav_info = {
            'current_path': current_path,
            'relative_path': os.path.relpath(current_path, project_path),
            'breadcrumbs': [],
            'folders': [],
            'files': [],
            'parent_path': None,
            'can_go_up': False
        }

        # Build breadcrumbs
        rel_path = nav_info['relative_path']
        if rel_path != '.':
            parts = rel_path.split(os.sep)
            breadcrumb_path = project_path
            nav_info['breadcrumbs'].append({
                'name': 'Project Root',
                'path': project_path,
                'is_current': False
            })

            for i, part in enumerate(parts):
                breadcrumb_path = os.path.join(breadcrumb_path, part)
                is_current = i == len(parts) - 1
                nav_info['breadcrumbs'].append({
                    'name': part,
                    'path': breadcrumb_path,
                    'is_current': is_current
                })
        else:
            nav_info['breadcrumbs'].append({
                'name': 'Project Root',
                'path': project_path,
                'is_current': True
            })

        # Check if we can go up
        if current_path != project_path:
            nav_info['parent_path'] = os.path.dirname(current_path)
            nav_info['can_go_up'] = True

        # Get folder contents
        try:
            for item in sorted(os.listdir(current_path)):
                item_path = os.path.join(current_path, item)

                if os.path.isdir(item_path):
                    # Count contents
                    try:
                        contents = os.listdir(item_path)
                        file_count = sum(1 for f in contents if os.path.isfile(os.path.join(item_path, f)))
                        folder_count = sum(1 for f in contents if os.path.isdir(os.path.join(item_path, f)))
                    except (OSError, PermissionError):
                        file_count = folder_count = 0

                    folder_info = {
                        'name': item,
                        'path': item_path,
                        'file_count': file_count,
                        'folder_count': folder_count,
                        'total_items': file_count + folder_count
                    }

                    # Get folder metadata
                    try:
                        stat_info = os.stat(item_path)
                        folder_info['modified'] = datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                        folder_info['created'] = datetime.fromtimestamp(stat_info.st_ctime).isoformat()
                    except (OSError, IOError):
                        folder_info['modified'] = folder_info['created'] = None

                    nav_info['folders'].append(folder_info)

                elif os.path.isfile(item_path):
                    try:
                        stat_info = os.stat(item_path)
                        file_info = {
                            'name': item,
                            'path': item_path,
                            'size': stat_info.st_size,
                            'modified': datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                            'created': datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                            'extension': os.path.splitext(item)[1].lower()
                        }
                        nav_info['files'].append(file_info)
                    except (OSError, IOError):
                        continue

        except (OSError, PermissionError):
            pass

        return nav_info

    def format_folder_navigation_display(self, project_id: str, current_path: str = None) -> str:
        """Format folder navigation display"""
        nav_info = self.get_folder_navigation_info(project_id, current_path)
        if not nav_info:
            return "❌ Unable to access project folder"

        lines = []

        # Breadcrumbs
        lines.append("🧭 **Navigation**")
        breadcrumb_parts = []
        for crumb in nav_info['breadcrumbs']:
            if crumb['is_current']:
                breadcrumb_parts.append(f"**{crumb['name']}**")
            else:
                breadcrumb_parts.append(f"[{crumb['name']}]({crumb['path']})")
        lines.append(" > ".join(breadcrumb_parts))
        lines.append("")

        # Current location info
        rel_path = nav_info['relative_path']
        if rel_path == '.':
            lines.append("📍 **Current Location:** Project Root")
        else:
            lines.append(f"📍 **Current Location:** {rel_path}")
        lines.append("")

        # Folders
        folders = nav_info['folders']
        if folders:
            lines.append(f"📁 **Folders ({len(folders)})**")
            for folder in folders:
                name = folder['name']
                total = folder['total_items']
                files = folder['file_count']
                subfolders = folder['folder_count']
                modified = self._format_datetime(folder.get('modified'))

                lines.append(f"• **{name}/** ({files} files, {subfolders} folders) - Modified {modified}")
            lines.append("")

        # Files
        files = nav_info['files']
        if files:
            lines.append(f"📄 **Files ({len(files)})**")
            for file_info in files:
                name = file_info['name']
                size = self.format_file_size(file_info['size'])
                modified = self._format_datetime(file_info.get('modified'))
                icon = self._get_file_icon(file_info.get('extension', ''))

                lines.append(f"• {icon} **{name}** ({size}) - Modified {modified}")
            lines.append("")

        # Navigation options
        if nav_info['can_go_up']:
            lines.append("⬆️ **Navigation Options**")
            lines.append(f"• [Go Up]({nav_info['parent_path']}) - Return to parent folder")
            lines.append("")

        if not folders and not files:
            lines.append("📭 **Empty Folder**")
            lines.append("This folder contains no files or subfolders.")

        return "\n".join(lines)

    def delete_file_from_project(self, project_id: str, file_id: str) -> bool:
        """Delete a file from a project"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        files = self.projects[project_id]['files']
        for i, file_info in enumerate(files):
            if file_info.get('id') == file_id:
                # Delete physical file
                file_path = file_info.get('path')
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except OSError:
                        pass  # File might be in use

                # Remove from project
                files.pop(i)
                self.projects[project_id]['updated_at'] = datetime.now().isoformat()
                self.save_projects()
                return True

        return False

    def _analyze_project_file_relationships(self, project_id: str):
        """Analyze relationships between files in a project"""
        if project_id not in self.projects:
            return

        input_files = [f for f in self.projects[project_id]['files'] if f.get('category') == 'input']

        if len(input_files) < 2:
            return  # No relationships to analyze with less than 2 files

        # Simple relationship analysis based on file metadata
        relationships = []

        for i, file1 in enumerate(input_files):
            for file2 in input_files[i+1:]:
                relationship_score = self._calculate_file_relationship_score(file1, file2)
                if relationship_score > 0.3:  # Threshold for related files
                    relationships.append({
                        'file1_id': file1['id'],
                        'file2_id': file2['id'],
                        'relationship_score': relationship_score,
                        'relationship_type': self._determine_relationship_type(file1, file2)
                    })

        # Store relationships in project metadata
        self.projects[project_id]['file_relationships'] = relationships
        self.save_projects()

    def _calculate_file_relationship_score(self, file1: Dict[str, Any], file2: Dict[str, Any]) -> float:
        """Calculate relationship score between two files"""
        score = 0.0

        metadata1 = file1.get('metadata', {})
        metadata2 = file2.get('metadata', {})

        # File type compatibility
        type1 = metadata1.get('file_type', 'unknown')
        type2 = metadata2.get('file_type', 'unknown')

        if type1 == type2:
            score += 0.2
        elif self._are_compatible_types(type1, type2):
            score += 0.4

        # Shared entities
        entities1 = set(metadata1.get('key_entities', []))
        entities2 = set(metadata2.get('key_entities', []))
        shared_entities = entities1.intersection(entities2)

        if shared_entities:
            score += min(len(shared_entities) * 0.2, 0.5)

        # API and UI relationship
        apis1 = metadata1.get('apis', [])
        ui_elements2 = metadata2.get('ui_elements', [])

        if apis1 and ui_elements2:
            score += 0.3  # API + UI likely related

        return min(score, 1.0)

    def _are_compatible_types(self, type1: str, type2: str) -> bool:
        """Check if two file types are compatible/related"""
        compatible_pairs = [
            ('api_specification', 'ui_requirements'),
            ('business_logic', 'ui_requirements'),
            ('business_logic', 'data_model'),
            ('api_specification', 'data_model'),
            ('security_requirements', 'api_specification'),
            ('security_requirements', 'ui_requirements')
        ]

        return (type1, type2) in compatible_pairs or (type2, type1) in compatible_pairs

    def _determine_relationship_type(self, file1: Dict[str, Any], file2: Dict[str, Any]) -> str:
        """Determine the type of relationship between two files"""
        metadata1 = file1.get('metadata', {})
        metadata2 = file2.get('metadata', {})

        type1 = metadata1.get('file_type', 'unknown')
        type2 = metadata2.get('file_type', 'unknown')

        if type1 == 'api_specification' and type2 == 'ui_requirements':
            return 'api_ui_integration'
        elif type1 == 'business_logic' and type2 in ['ui_requirements', 'api_specification']:
            return 'business_implementation'
        elif type1 == 'data_model' and type2 in ['api_specification', 'business_logic']:
            return 'data_integration'
        else:
            return 'functional_overlap'

    def get_project_file_relationships(self, project_id: str) -> List[Dict[str, Any]]:
        """Get file relationships for a project"""
        project = self.get_project(project_id)
        if not project:
            return []
        return project.get('file_relationships', [])

    def get_related_files_groups(self, project_id: str) -> List[List[Dict[str, Any]]]:
        """Get groups of related files"""
        relationships = self.get_project_file_relationships(project_id)
        input_files = [f for f in self.get_project_files(project_id) if f.get('category') == 'input']

        if not relationships:
            return [[f] for f in input_files]  # Each file is its own group

        # Build groups based on relationships
        file_groups = []
        processed_files = set()

        for file_info in input_files:
            if file_info['id'] in processed_files:
                continue

            # Find all files related to this one
            related_group = [file_info]
            processed_files.add(file_info['id'])

            # Find directly related files
            for rel in relationships:
                if rel['file1_id'] == file_info['id']:
                    related_file = next((f for f in input_files if f['id'] == rel['file2_id']), None)
                    if related_file and related_file['id'] not in processed_files:
                        related_group.append(related_file)
                        processed_files.add(related_file['id'])
                elif rel['file2_id'] == file_info['id']:
                    related_file = next((f for f in input_files if f['id'] == rel['file1_id']), None)
                    if related_file and related_file['id'] not in processed_files:
                        related_group.append(related_file)
                        processed_files.add(related_file['id'])

            file_groups.append(related_group)

        return file_groups

    def get_file_stats(self, project_id: str) -> Dict[str, Any]:
        """Get file statistics for a project"""
        files = self.get_project_files(project_id)
        categorized = self.get_project_files_by_category(project_id)

        total_size = sum(file_info.get('file_size', 0) for file_info in files)

        return {
            'total_files': len(files),
            'input_files': len(categorized['input']),
            'output_files': len(categorized['output']),
            'total_size': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2) if total_size > 0 else 0
        }

    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"
