# API Keys for Test Case Generator
# Copy this file to .env and add your actual API keys

# OpenAI API Key for ChatGPT
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API Key
# Get your key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Model configurations
OPENAI_MODEL=gpt-3.5-turbo
GEMINI_MODEL=gemini-pro

# Optional: API settings
OPENAI_MAX_TOKENS=4000
GEMINI_MAX_TOKENS=4000
API_TIMEOUT=30
