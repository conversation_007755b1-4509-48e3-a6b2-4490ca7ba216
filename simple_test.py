#!/usr/bin/env python3
import cv2
import numpy as np

# Test cv2.getTextSize
text = "Morning Vibes"
font = cv2.FONT_HERSHEY_TRIPLEX
font_scale = 4.5
thickness = 5

print("Testing cv2.getTextSize...")
try:
    result = cv2.getTextSize(text, font, font_scale, thickness)
    print(f"Result type: {type(result)}")
    print(f"Result: {result}")
    print(f"Length: {len(result)}")
    
    # Test unpacking
    (text_width, text_height) = result[0]
    baseline = result[1]
    print(f"Text size: {text_width}x{text_height}")
    print(f"Baseline: {baseline}")
    
    print("✅ cv2.getTextSize works correctly")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

# Test glow effects
print("\nTesting glow effects...")
try:
    glow_effects = [
        (8, 50),      # <PERSON> đậm nhất
        (6, 100),     # Shadow trung bình  
        (4, 150),     # Shadow nhẹ
        (2, 200)      # Glow nhẹ
    ]
    
    for offset, glow_intensity in glow_effects:
        print(f"Offset: {offset}, Intensity: {glow_intensity}")
    
    print("✅ Glow effects work correctly")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
