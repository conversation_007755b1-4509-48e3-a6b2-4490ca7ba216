#!/usr/bin/env python3
"""
Simple URL test
"""

from youtube_audio_downloader import YouTubeAudioDownloader

def main():
    downloader = YouTubeAudioDownloader()
    
    # Test URL có escape characters
    test_url = r"https://www.youtube.com/watch\?v\=dQw4w9WgXcQ"
    
    print("=== Simple URL Test ===")
    print(f"Original URL: {test_url}")
    
    # Clean URL
    clean_url = downloader.clean_url(test_url)
    print(f"Cleaned URL: {clean_url}")
    
    # Extract video ID
    video_id = downloader.extract_video_id(clean_url)
    print(f"Video ID: {video_id}")
    
    # Validate
    is_valid = downloader.is_valid_youtube_url(test_url)
    print(f"Valid: {'✅ YES' if is_valid else '❌ NO'}")
    
    print("\n=== Test với URL bạn đã dùng ===")
    your_url = r"https://www.youtube.com/watch\?v\=9wOKPD0VMQw\&list\=RDfvDL3narpA0\&index\=5"
    print(f"Your URL: {your_url}")
    
    clean_your_url = downloader.clean_url(your_url)
    print(f"Cleaned: {clean_your_url}")
    
    your_video_id = downloader.extract_video_id(clean_your_url)
    print(f"Video ID: {your_video_id}")
    
    is_valid_yours = downloader.is_valid_youtube_url(your_url)
    print(f"Valid: {'✅ YES' if is_valid_yours else '❌ NO'}")
    
    if is_valid_yours:
        print("\n🎉 URL validation FIX thành công!")
        print("Bây giờ bạn có thể tải audio từ URL này.")
    else:
        print("\n❌ Vẫn còn vấn đề với URL validation")

if __name__ == "__main__":
    main()
