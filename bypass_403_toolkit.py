#!/usr/bin/env python3
"""
Toolkit để bypass lỗi 403 khi download YouTube
"""

import os
import sys
import json
import requests
import subprocess
from pathlib import Path
from typing import List, Dict, Any

class Bypass403Toolkit:
    """Toolkit các phương pháp bypass 403"""
    
    def __init__(self):
        self.config_file = Path("bypass_config.json")
        self.load_config()
    
    def load_config(self):
        """Load cấu hình"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            self.config = {
                'proxies': [],
                'user_agents': [],
                'vpn_countries': ['US', 'UK', 'CA', 'AU', 'DE', 'JP'],
                'cookies_file': 'youtube_cookies.txt'
            }
            self.save_config()
    
    def save_config(self):
        """<PERSON><PERSON><PERSON> c<PERSON> hình"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def get_free_proxies(self) -> List[str]:
        """Lấy danh sách proxy miễn phí"""
        print("🔍 Đang tìm proxy miễn phí...")
        
        proxies = []
        
        try:
            # Thử một số API proxy miễn phí
            apis = [
                'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all',
                'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
            ]
            
            for api in apis:
                try:
                    response = requests.get(api, timeout=10)
                    if response.status_code == 200:
                        proxy_list = response.text.strip().split('\n')
                        for proxy in proxy_list[:10]:  # Lấy 10 proxy đầu
                            if ':' in proxy:
                                proxies.append(f"http://{proxy.strip()}")
                except:
                    continue
                    
        except Exception as e:
            print(f"❌ Lỗi khi lấy proxy: {e}")
        
        if proxies:
            print(f"✅ Tìm thấy {len(proxies)} proxy")
            self.config['proxies'] = proxies
            self.save_config()
        else:
            print("❌ Không tìm thấy proxy nào")
        
        return proxies
    
    def test_proxy(self, proxy: str) -> bool:
        """Test proxy có hoạt động không"""
        try:
            response = requests.get(
                'https://httpbin.org/ip',
                proxies={'http': proxy, 'https': proxy},
                timeout=10
            )
            return response.status_code == 200
        except:
            return False
    
    def find_working_proxies(self) -> List[str]:
        """Tìm proxy hoạt động"""
        print("🧪 Đang test proxy...")
        
        if not self.config['proxies']:
            self.get_free_proxies()
        
        working_proxies = []
        
        for i, proxy in enumerate(self.config['proxies'][:20]):  # Test 20 proxy đầu
            print(f"Testing {i+1}/20: {proxy[:30]}...", end=' ')
            
            if self.test_proxy(proxy):
                print("✅")
                working_proxies.append(proxy)
            else:
                print("❌")
        
        print(f"\n🎉 Tìm thấy {len(working_proxies)} proxy hoạt động")
        return working_proxies
    
    def create_cookies_extractor(self):
        """Tạo script extract cookies từ browser"""
        
        script_content = '''#!/usr/bin/env python3
"""
Extract YouTube cookies từ browser
"""

import os
import sys
import json
import sqlite3
import shutil
from pathlib import Path

def extract_chrome_cookies():
    """Extract cookies từ Chrome"""
    
    # Đường dẫn Chrome cookies trên các OS
    chrome_paths = {
        'Windows': Path.home() / 'AppData/Local/Google/Chrome/User Data/Default/Cookies',
        'Darwin': Path.home() / 'Library/Application Support/Google/Chrome/Default/Cookies',
        'Linux': Path.home() / '.config/google-chrome/Default/Cookies'
    }
    
    import platform
    system = platform.system()
    
    if system not in chrome_paths:
        print("❌ Không hỗ trợ OS này")
        return False
    
    cookies_path = chrome_paths[system]
    
    if not cookies_path.exists():
        print("❌ Không tìm thấy Chrome cookies")
        return False
    
    # Copy cookies file để tránh lock
    temp_cookies = Path("temp_cookies.db")
    shutil.copy2(cookies_path, temp_cookies)
    
    try:
        conn = sqlite3.connect(temp_cookies)
        cursor = conn.cursor()
        
        # Query YouTube cookies
        cursor.execute("""
            SELECT name, value, domain, path, expires_utc, is_secure, is_httponly
            FROM cookies 
            WHERE domain LIKE '%youtube.com%' OR domain LIKE '%google.com%'
        """)
        
        cookies = cursor.fetchall()
        
        # Tạo file cookies.txt format Netscape
        with open("youtube_cookies.txt", "w") as f:
            f.write("# Netscape HTTP Cookie File\\n")
            f.write("# Generated by YouTube Downloader\\n")
            
            for cookie in cookies:
                name, value, domain, path, expires, secure, httponly = cookie
                
                # Convert expires time
                expires_str = str(expires) if expires else "0"
                secure_str = "TRUE" if secure else "FALSE"
                
                line = f"{domain}\\tTRUE\\t{path}\\t{secure_str}\\t{expires_str}\\t{name}\\t{value}\\n"
                f.write(line)
        
        print("✅ Đã tạo youtube_cookies.txt")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
    finally:
        conn.close()
        temp_cookies.unlink(missing_ok=True)

if __name__ == "__main__":
    print("🍪 YouTube Cookies Extractor")
    print("Đảm bảo đã đóng Chrome trước khi chạy!")
    
    input("Nhấn Enter để tiếp tục...")
    extract_chrome_cookies()
'''
        
        with open("extract_cookies.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("📝 Đã tạo extract_cookies.py")
        print("💡 Chạy: python extract_cookies.py để lấy cookies")
    
    def create_vpn_guide(self):
        """Tạo hướng dẫn VPN"""
        
        guide = """
# Hướng dẫn sử dụng VPN để bypass 403

## VPN miễn phí:
1. **ProtonVPN** - Có server miễn phí
2. **Windscribe** - 10GB/tháng miễn phí  
3. **TunnelBear** - 500MB/tháng miễn phí

## VPN command line:
```bash
# OpenVPN
sudo openvpn --config config.ovpn

# WireGuard
sudo wg-quick up wg0
```

## Proxy browser extension:
1. **FoxyProxy** (Chrome/Firefox)
2. **Proxy SwitchyOmega** (Chrome)

## Test IP sau khi dùng VPN:
```bash
curl https://ipinfo.io
```

## Các quốc gia nên thử:
- US (Mỹ) - Ít hạn chế nhất
- UK (Anh) 
- Canada
- Australia
- Germany
- Japan

## Lưu ý:
- Một số video chỉ có ở một số quốc gia
- Thử nhiều server khác nhau
- Tránh server quá tải
"""
        
        with open("vpn_guide.md", "w", encoding="utf-8") as f:
            f.write(guide)
        
        print("📝 Đã tạo vpn_guide.md")
    
    def create_comprehensive_guide(self):
        """Tạo hướng dẫn toàn diện bypass 403"""
        
        guide = """
# Hướng dẫn toàn diện bypass lỗi 403 YouTube

## Nguyên nhân lỗi 403:
1. **Geo-blocking** - Video bị chặn theo vùng
2. **Rate limiting** - Quá nhiều request
3. **Bot detection** - YouTube phát hiện bot
4. **Premium content** - Nội dung trả phí
5. **Private/Unlisted** - Video riêng tư

## Giải pháp theo thứ tự ưu tiên:

### 1. VPN (Hiệu quả nhất)
- Đổi IP sang quốc gia khác
- Thử US, UK, Canada trước
- Tránh server free quá tải

### 2. Proxy
- Sử dụng HTTP/HTTPS proxy
- Rotate nhiều proxy khác nhau
- Test proxy trước khi dùng

### 3. User Agent Rotation
- Giả mạo browser khác nhau
- Thêm headers thực tế
- Random delay giữa requests

### 4. Cookies
- Đăng nhập YouTube trước
- Extract cookies từ browser
- Sử dụng cookies khi download

### 5. Alternative Methods
- Thử mobile user agent
- Sử dụng embedded URL
- Thử API keys khác nhau

## Tools hỗ trợ:
1. `advanced_youtube_downloader.py` - Auto fallback
2. `extract_cookies.py` - Lấy cookies
3. VPN clients
4. Proxy checkers

## Lưu ý pháp lý:
- Chỉ download nội dung bạn có quyền
- Tôn trọng bản quyền
- Không abuse hệ thống
- Sử dụng cho mục đích cá nhân

## Troubleshooting:
- Thử lại sau vài phút
- Đổi DNS (*******, *******)
- Clear browser cache
- Update yt-dlp: `pip install -U yt-dlp`
"""
        
        with open("bypass_403_guide.md", "w", encoding="utf-8") as f:
            f.write(guide)
        
        print("📝 Đã tạo bypass_403_guide.md")

def main():
    """Main function"""
    toolkit = Bypass403Toolkit()
    
    print("🛠️  Bypass 403 Toolkit")
    print("="*40)
    
    while True:
        print("\nChọn chức năng:")
        print("1. Tìm proxy miễn phí")
        print("2. Test proxy hiện có")
        print("3. Tạo cookies extractor")
        print("4. Tạo VPN guide")
        print("5. Tạo hướng dẫn toàn diện")
        print("6. Thoát")
        
        choice = input("\nLựa chọn (1-6): ").strip()
        
        if choice == '1':
            toolkit.get_free_proxies()
        elif choice == '2':
            working = toolkit.find_working_proxies()
            if working:
                print("✅ Proxy hoạt động:")
                for proxy in working[:5]:
                    print(f"   {proxy}")
        elif choice == '3':
            toolkit.create_cookies_extractor()
        elif choice == '4':
            toolkit.create_vpn_guide()
        elif choice == '5':
            toolkit.create_comprehensive_guide()
        elif choice == '6':
            print("👋 Tạm biệt!")
            break
        else:
            print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
