# 🎵 YouTube Audio Downloader

Tải audio từ YouTube với chất lượng cao, hỗ trợ convert URL raw và xử lý lỗi 403.

## 📁 Files

- `youtube_audio_downloader.py` - <PERSON><PERSON><PERSON> ch<PERSON>h
- `download_youtube_audio.py` - **<PERSON><PERSON><PERSON> di<PERSON>n đơ<PERSON> (khuyến nghị)**
- `youtube_downloader_guide.md` - Hướng dẫn chi tiết
- `youtube_audio/` - Thư mục chứa file audio đã tải

## 🚀 Cách sử dụng nhanh

### 1. Cài đặt
```bash
pip install yt-dlp ffmpeg-python
```

### 2. Tải audio
```bash
# Interactive mode (dễ nhất)
python download_youtube_audio.py

# Command line
python download_youtube_audio.py "https://www.youtube.com/watch?v=VIDEO_ID"

# Với tên file tùy chỉnh
python download_youtube_audio.py "URL" "tên_file"
```

## ✨ Tính năng

- ✅ **Auto URL Convert** - T<PERSON> động convert URL có playlist/radio/timestamp
- ✅ **High Quality** - MP3 192kbps
- ✅ **Smart Naming** - Tự động làm sạch tên file
- ✅ **Error Handling** - Xử lý lỗi và gợi ý khắc phục
- ✅ **Multiple Formats** - Hỗ trợ nhiều định dạng URL YouTube
- ✅ **Debug Mode** - Hiển thị thông tin chi tiết

## 🔄 URL Support

Hỗ trợ tất cả định dạng URL YouTube:

```bash
# Standard
https://www.youtube.com/watch?v=VIDEO_ID

# With playlist (auto remove playlist)
https://www.youtube.com/watch?v=VIDEO_ID&list=PLAYLIST_ID

# With radio (auto remove radio)
https://www.youtube.com/watch?v=VIDEO_ID&list=RD...&start_radio=1

# With timestamp (auto remove timestamp)
https://www.youtube.com/watch?v=VIDEO_ID&t=30s

# Short URL
https://youtu.be/VIDEO_ID

# Mobile
https://m.youtube.com/watch?v=VIDEO_ID
```

## 📂 Output

- Thư mục: `youtube_audio/`
- Format: MP3 192kbps
- Tên file: Tự động từ tiêu đề video (có thể tùy chỉnh)

## 🛠️ Troubleshooting

**Lỗi 403 Forbidden:**
- Sử dụng VPN
- Thử lại sau vài phút
- Kiểm tra video có public không

**Lỗi FFmpeg:**
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Conda
conda install -c conda-forge ffmpeg
```

**Update yt-dlp:**
```bash
pip install -U yt-dlp
```

## 📖 Ví dụ

```python
from youtube_audio_downloader import YouTubeAudioDownloader

# Tạo downloader
downloader = YouTubeAudioDownloader()

# Tải audio
url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
success = downloader.download_audio(url, "Rick Roll")

if success:
    print("✅ Tải thành công!")
```

## ⚖️ Lưu ý

- Chỉ tải nội dung bạn có quyền sử dụng
- Tôn trọng bản quyền
- Sử dụng cho mục đích cá nhân

---

**🎉 Enjoy your music!** 🎵
