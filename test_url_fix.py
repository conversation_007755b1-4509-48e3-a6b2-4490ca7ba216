#!/usr/bin/env python3
"""
Test URL fix cho YouTube downloader
"""

from youtube_audio_downloader import YouTubeAudioDownloader

def test_problematic_url():
    """Test URL có vấn đề"""
    downloader = YouTubeAudioDownloader()
    
    # URL có escape characters và playlist parameters
    problematic_url = r"https://www.youtube.com/watch\?v\=9wOKPD0VMQw\&list\=RDfvDL3narpA0\&index\=5"
    
    print("=== Test URL Fix ===")
    print(f"URL gốc: {problematic_url}")
    
    # Test clean_url
    clean_url = downloader.clean_url(problematic_url)
    print(f"URL sau khi làm sạch: {clean_url}")
    
    # Test extract_video_id
    video_id = downloader.extract_video_id(clean_url)
    print(f"Video ID: {video_id}")
    
    # Test validation
    is_valid = downloader.is_valid_youtube_url(problematic_url)
    print(f"URL hợp lệ: {'✅ Có' if is_valid else '❌ Không'}")
    
    if is_valid:
        # Test get info
        print("\n📋 Lấy thông tin video...")
        info = downloader.get_video_info(clean_url)
        if info:
            print(f"✅ Tiêu đề: {info['title']}")
            print(f"👤 Kênh: {info['uploader']}")
            duration_min = info['duration'] // 60
            duration_sec = info['duration'] % 60
            print(f"⏱️  Thời lượng: {duration_min}:{duration_sec:02d}")
        else:
            print("❌ Không thể lấy thông tin video")
    
    print("\n" + "="*50)

def test_various_urls():
    """Test nhiều loại URL khác nhau"""
    downloader = YouTubeAudioDownloader()
    
    test_urls = [
        # URL có escape characters
        r"https://www.youtube.com/watch\?v\=dQw4w9WgXcQ",
        
        # URL có playlist
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy6nuLMt9xaJGA6H_VjlrBW",
        
        # URL có timestamp
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s",
        
        # URL ngắn
        "https://youtu.be/dQw4w9WgXcQ",
        
        # URL mobile
        "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
    ]
    
    print("=== Test Nhiều Loại URL ===")
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{i}. URL: {url}")
        
        # Clean URL
        clean_url = downloader.clean_url(url)
        if clean_url != url:
            print(f"   Cleaned: {clean_url}")
        
        # Extract video ID
        video_id = downloader.extract_video_id(clean_url)
        print(f"   Video ID: {video_id}")
        
        # Validate
        is_valid = downloader.is_valid_youtube_url(url)
        print(f"   Valid: {'✅' if is_valid else '❌'}")

if __name__ == "__main__":
    test_problematic_url()
    test_various_urls()
