#!/usr/bin/env python3
"""
Advanced YouTube Audio Downloader v<PERSON><PERSON> c<PERSON><PERSON> kỹ thuật bypass 403
"""

import os
import sys
import re
import time
import random
from pathlib import Path
from typing import Optional, Dict, Any, List

try:
    import yt_dlp
    import requests
except ImportError:
    print("Cần cài đặt: pip install yt-dlp requests")
    sys.exit(1)


class AdvancedYouTubeDownloader:
    """Advanced YouTube downloader với bypass techniques"""
    
    def __init__(self, output_dir: str = "downloads"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # User agents để rotate
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
        ]
        
        # Proxy list (có thể thêm proxy của bạn)
        self.proxies = [
            # Thêm proxy nếu có
            # 'http://proxy1:port',
            # 'http://proxy2:port',
        ]
    
    def get_random_user_agent(self) -> str:
        """Lấy random user agent"""
        return random.choice(self.user_agents)
    
    def get_random_proxy(self) -> Optional[str]:
        """Lấy random proxy"""
        if self.proxies:
            return random.choice(self.proxies)
        return None
    
    def create_ydl_options(self, method: str = "default") -> Dict[str, Any]:
        """Tạo options cho yt-dlp với các phương pháp khác nhau"""
        
        base_opts = {
            'format': 'bestaudio/best',
            'outtmpl': str(self.output_dir / '%(title)s.%(ext)s'),
            'extractaudio': True,
            'audioformat': 'mp3',
            'audioquality': '192',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
            'noplaylist': True,
        }
        
        if method == "stealth":
            # Phương pháp stealth - giả mạo browser
            base_opts.update({
                'http_headers': {
                    'User-Agent': self.get_random_user_agent(),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-us,en;q=0.5',
                    'Accept-Encoding': 'gzip,deflate',
                    'Accept-Charset': 'ISO-8859-1,utf-8;q=0.7,*;q=0.7',
                    'Keep-Alive': '300',
                    'Connection': 'keep-alive',
                },
                'sleep_interval': random.uniform(1, 3),
                'max_sleep_interval': 5,
            })
            
        elif method == "mobile":
            # Giả mạo mobile browser
            base_opts.update({
                'http_headers': {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                },
            })
            
        elif method == "cookies":
            # Sử dụng cookies (nếu có)
            cookies_file = Path("youtube_cookies.txt")
            if cookies_file.exists():
                base_opts['cookiefile'] = str(cookies_file)
                
        elif method == "proxy":
            # Sử dụng proxy
            proxy = self.get_random_proxy()
            if proxy:
                base_opts['proxy'] = proxy
                
        elif method == "geo_bypass":
            # Bypass geo-restriction
            base_opts.update({
                'geo_bypass': True,
                'geo_bypass_country': 'US',  # Thử với US
            })
            
        return base_opts
    
    def download_with_fallback(self, url: str, custom_filename: Optional[str] = None) -> bool:
        """Tải với nhiều phương pháp fallback"""
        
        methods = [
            ("default", "Phương pháp mặc định"),
            ("stealth", "Stealth mode (giả mạo browser)"),
            ("mobile", "Mobile browser"),
            ("geo_bypass", "Geo bypass"),
            ("cookies", "Sử dụng cookies"),
            ("proxy", "Sử dụng proxy"),
        ]
        
        for method, description in methods:
            print(f"\n🔄 Thử {description}...")
            
            try:
                opts = self.create_ydl_options(method)
                
                if custom_filename:
                    clean_filename = re.sub(r'[<>:"/\\|?*]', '_', custom_filename)
                    opts['outtmpl'] = str(self.output_dir / f'{clean_filename}.%(ext)s')
                
                with yt_dlp.YoutubeDL(opts) as ydl:
                    # Thêm delay random để tránh rate limiting
                    time.sleep(random.uniform(0.5, 2.0))
                    
                    ydl.download([url])
                    print(f"✅ Thành công với {description}!")
                    return True
                    
            except Exception as e:
                print(f"❌ {description} thất bại: {str(e)[:100]}...")
                continue
        
        return False
    
    def extract_video_info_advanced(self, url: str) -> Optional[Dict[str, Any]]:
        """Lấy thông tin video với advanced methods"""
        
        methods = ["stealth", "mobile", "geo_bypass"]
        
        for method in methods:
            try:
                opts = self.create_ydl_options(method)
                opts['quiet'] = True
                
                with yt_dlp.YoutubeDL(opts) as ydl:
                    info = ydl.extract_info(url, download=False)
                    return {
                        'title': info.get('title', 'Unknown'),
                        'duration': info.get('duration', 0),
                        'uploader': info.get('uploader', 'Unknown'),
                        'view_count': info.get('view_count', 0),
                        'upload_date': info.get('upload_date', 'Unknown'),
                        'availability': info.get('availability', 'Unknown'),
                    }
            except Exception:
                continue
        
        return None
    
    def check_video_availability(self, url: str) -> Dict[str, Any]:
        """Kiểm tra tình trạng video"""
        
        print("🔍 Kiểm tra tình trạng video...")
        
        # Thử lấy thông tin cơ bản
        info = self.extract_video_info_advanced(url)
        
        if not info:
            return {
                'available': False,
                'reason': 'Không thể lấy thông tin video',
                'suggestions': ['Kiểm tra URL', 'Thử VPN', 'Video có thể bị xóa']
            }
        
        availability = info.get('availability', '').lower()
        
        if 'private' in availability:
            return {
                'available': False,
                'reason': 'Video bị private',
                'suggestions': ['Liên hệ chủ video để public']
            }
        elif 'premium' in availability:
            return {
                'available': False,
                'reason': 'Video yêu cầu YouTube Premium',
                'suggestions': ['Cần tài khoản Premium', 'Thử với cookies từ tài khoản Premium']
            }
        elif 'region' in availability or 'country' in availability:
            return {
                'available': False,
                'reason': 'Video bị chặn theo vùng địa lý',
                'suggestions': ['Sử dụng VPN', 'Thử geo bypass', 'Sử dụng proxy']
            }
        else:
            return {
                'available': True,
                'reason': 'Video có thể tải được',
                'info': info
            }
    
    def download_advanced(self, url: str, custom_filename: Optional[str] = None) -> bool:
        """Download với tất cả kỹ thuật advanced"""
        
        print(f"🎵 Advanced YouTube Downloader")
        print(f"🔗 URL: {url}")
        
        # Kiểm tra tình trạng video
        status = self.check_video_availability(url)
        
        if not status['available']:
            print(f"❌ {status['reason']}")
            print("💡 Gợi ý:")
            for suggestion in status['suggestions']:
                print(f"   - {suggestion}")
            
            # Vẫn thử download với fallback methods
            print("\n🔄 Thử download với các phương pháp khác...")
            return self.download_with_fallback(url, custom_filename)
        else:
            print("✅ Video có thể tải được")
            info = status['info']
            print(f"📺 Tiêu đề: {info['title']}")
            print(f"👤 Kênh: {info['uploader']}")
            
            # Thử download
            return self.download_with_fallback(url, custom_filename)


def create_cookies_guide():
    """Tạo hướng dẫn lấy cookies"""
    guide = """
# Hướng dẫn lấy YouTube Cookies để bypass 403

## Cách 1: Sử dụng Browser Extension
1. Cài extension "Get cookies.txt" cho Chrome/Firefox
2. Đăng nhập YouTube
3. Mở extension và export cookies
4. Lưu file cookies.txt vào thư mục này

## Cách 2: Manual từ Developer Tools
1. Đăng nhập YouTube
2. F12 -> Application/Storage -> Cookies -> youtube.com
3. Copy tất cả cookies
4. Tạo file youtube_cookies.txt theo format Netscape

## Cách 3: Sử dụng yt-dlp để extract
```bash
yt-dlp --cookies-from-browser chrome --write-info-json --skip-download "URL"
```

Lưu ý: Chỉ sử dụng cookies của chính bạn!
"""
    
    with open("cookies_guide.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📝 Đã tạo file cookies_guide.md")


def main():
    """Demo advanced downloader"""
    
    if len(sys.argv) < 2:
        print("🎵 Advanced YouTube Audio Downloader")
        print("\nCách sử dụng:")
        print("python advanced_youtube_downloader.py 'URL'")
        print("python advanced_youtube_downloader.py 'URL' 'tên_file'")
        print("\nHoặc chạy interactive mode:")
        
        downloader = AdvancedYouTubeDownloader()
        
        while True:
            url = input("\nNhập URL YouTube (hoặc 'quit'): ").strip()
            if url.lower() in ['quit', 'exit', 'q']:
                break
            
            if url:
                custom_name = input("Tên file tùy chỉnh (Enter để bỏ qua): ").strip()
                success = downloader.download_advanced(
                    url, 
                    custom_name if custom_name else None
                )
                
                if success:
                    print("🎉 Hoàn thành!")
                else:
                    print("😞 Không thể tải được")
    else:
        url = sys.argv[1]
        custom_name = sys.argv[2] if len(sys.argv) > 2 else None
        
        downloader = AdvancedYouTubeDownloader()
        success = downloader.download_advanced(url, custom_name)
        
        if not success:
            print("\n💡 Gợi ý thêm:")
            print("1. Thử với VPN")
            print("2. Sử dụng cookies (chạy create_cookies_guide())")
            print("3. Thử lại sau vài phút")
            create_cookies_guide()


if __name__ == "__main__":
    main()
