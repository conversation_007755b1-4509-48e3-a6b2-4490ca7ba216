#!/usr/bin/env python3
"""
Test script cho YouTube Audio Downloader
"""

from youtube_audio_downloader import YouTubeAudioDownloader

def test_url_validation():
    """Test các URL khác nhau"""
    downloader = YouTubeAudioDownloader()
    
    # Test URLs
    test_urls = [
        # Valid URLs
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=10s",
        "https://youtube.com/watch?v=dQw4w9WgXcQ",
        
        # Invalid URLs
        "https://www.google.com",
        "not a url",
        "",
        "https://vimeo.com/123456",
        "youtube.com",  # Missing protocol
    ]
    
    print("=== Test URL Validation ===")
    for url in test_urls:
        is_valid = downloader.is_valid_youtube_url(url)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        print(f"{status}: {url}")
    
    print("\n" + "="*50)

def test_video_info():
    """Test lấy thông tin video"""
    downloader = YouTubeAudioDownloader()
    
    # URL test (Rick Roll - video công khai nổi tiếng)
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    print("=== Test Video Info ===")
    print(f"URL: {test_url}")
    
    info = downloader.get_video_info(test_url)
    if info:
        print("✅ Lấy thông tin thành công:")
        print(f"   Tiêu đề: {info['title']}")
        print(f"   Kênh: {info['uploader']}")
        print(f"   Thời lượng: {info['duration']} giây")
        print(f"   Lượt xem: {info['view_count']:,}")
    else:
        print("❌ Không thể lấy thông tin video")
    
    print("\n" + "="*50)

def interactive_test():
    """Test tương tác với người dùng"""
    downloader = YouTubeAudioDownloader("test_downloads")
    
    print("=== Test Tương Tác ===")
    print("Nhập URL YouTube để test (hoặc 'quit' để thoát):")
    
    while True:
        url = input("\nURL: ").strip()
        
        if url.lower() in ['quit', 'exit', 'q']:
            break
        
        if not url:
            continue
        
        print(f"\n🔍 Kiểm tra URL: {url}")
        
        # Test validation
        is_valid = downloader.is_valid_youtube_url(url)
        print(f"Validation: {'✅ Hợp lệ' if is_valid else '❌ Không hợp lệ'}")
        
        if is_valid:
            # Test get info
            print("📋 Lấy thông tin video...")
            info = downloader.get_video_info(url)
            if info:
                print(f"   Tiêu đề: {info['title']}")
                print(f"   Kênh: {info['uploader']}")
                duration_min = info['duration'] // 60
                duration_sec = info['duration'] % 60
                print(f"   Thời lượng: {duration_min}:{duration_sec:02d}")
                
                # Hỏi có muốn tải không
                download = input("\n💾 Có muốn tải audio không? (y/n): ").strip().lower()
                if download in ['y', 'yes', 'có']:
                    print("🎵 Bắt đầu tải...")
                    success = downloader.download_audio(url, debug=True)
                    if success:
                        print("🎉 Tải thành công!")
                    else:
                        print("😞 Tải thất bại!")
            else:
                print("❌ Không thể lấy thông tin video")

def main():
    """Main function"""
    print("🎵 YouTube Audio Downloader - Test Script")
    print("="*50)
    
    # Test URL validation
    test_url_validation()
    
    # Test video info với URL mẫu
    test_video_info()
    
    # Interactive test
    try:
        interactive_test()
    except KeyboardInterrupt:
        print("\n\n👋 Tạm biệt!")

if __name__ == "__main__":
    main()
