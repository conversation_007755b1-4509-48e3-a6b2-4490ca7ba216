#!/usr/bin/env python3
"""
Test URL Converter cho YouTube URLs
"""

from youtube_audio_downloader import YouTubeAudioDownloader

def test_url_converter():
    """Test chức năng convert URL"""
    downloader = YouTubeAudioDownloader()
    
    # Test URLs với các tham số khác nhau
    test_urls = [
        # URL của bạn
        "https://www.youtube.com/watch?v=fvDL3narpA0&list=RDfvDL3narpA0&start_radio=1",
        
        # URL với playlist
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy6nuLMt9xaJGA6H_VjlrBW&index=1",
        
        # URL với timestamp
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s",
        
        # URL với nhiều tham số
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLtest&index=5&t=1m30s&feature=youtu.be",
        
        # URL ngắn
        "https://youtu.be/dQw4w9WgXcQ?t=45",
        
        # URL mobile
        "https://m.youtube.com/watch?v=dQw4w9WgXcQ&list=PLtest",
        
        # URL với escape characters
        r"https://www.youtube.com/watch\?v\=dQw4w9WgXcQ\&list\=PLtest",
        
        # URL embed
        "https://www.youtube.com/embed/dQw4w9WgXcQ?start=30",
    ]
    
    print("🔄 Test URL Converter")
    print("=" * 80)
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{i}. Test URL:")
        print(f"   Original: {url}")
        
        # Analyze URL
        analysis = downloader.analyze_url_components(url)
        
        print(f"   Video ID: {analysis['video_id']}")
        print(f"   Domain: {analysis['domain']}")
        print(f"   Short URL: {'Yes' if analysis['is_short_url'] else 'No'}")
        
        # Show parameters
        if analysis['parameters']:
            print(f"   Parameters:")
            for key, value in analysis['parameters'].items():
                print(f"     - {key}: {value}")
        
        # Show flags
        flags = []
        if analysis['has_playlist']:
            flags.append(f"Playlist ({analysis.get('playlist_id', 'N/A')})")
        if analysis['has_timestamp']:
            flags.append(f"Timestamp ({analysis.get('timestamp', 'N/A')})")
        if analysis['has_radio']:
            flags.append("Radio")
        
        if flags:
            print(f"   Flags: {', '.join(flags)}")
        
        # Convert URL
        downloadable_url = downloader.convert_raw_url_to_downloadable(url)
        print(f"   Downloadable: {downloadable_url}")
        
        # Validate
        is_valid = downloader.is_valid_youtube_url(downloadable_url)
        print(f"   Valid: {'✅ Yes' if is_valid else '❌ No'}")
        
        print("-" * 80)

def test_specific_url():
    """Test URL cụ thể của bạn"""
    downloader = YouTubeAudioDownloader()
    
    your_url = "https://www.youtube.com/watch?v=fvDL3narpA0&list=RDfvDL3narpA0&start_radio=1"
    
    print("\n🎯 Test URL của bạn:")
    print("=" * 50)
    print(f"URL gốc: {your_url}")
    
    # Analyze
    analysis = downloader.analyze_url_components(your_url)
    
    print(f"\n📋 Phân tích:")
    print(f"   Video ID: {analysis['video_id']}")
    print(f"   Has playlist: {analysis['has_playlist']}")
    print(f"   Has radio: {analysis['has_radio']}")
    print(f"   Playlist ID: {analysis.get('playlist_id', 'N/A')}")
    
    # Convert
    downloadable_url = analysis['downloadable_url']
    print(f"\n🔄 URL sau convert: {downloadable_url}")
    
    # Test download info
    print(f"\n📺 Thông tin video:")
    info = downloader.get_video_info(downloadable_url)
    if info:
        print(f"   Tiêu đề: {info['title']}")
        print(f"   Kênh: {info['uploader']}")
        duration_min = info['duration'] // 60
        duration_sec = info['duration'] % 60
        print(f"   Thời lượng: {duration_min}:{duration_sec:02d}")
        
        # Ask for download
        download = input(f"\n💾 Tải audio này? (y/n): ").strip().lower()
        if download in ['y', 'yes', 'có']:
            print("🎵 Bắt đầu tải...")
            success = downloader.download_audio(downloadable_url, debug=True)
            if success:
                print("🎉 Tải thành công!")
            else:
                print("😞 Tải thất bại!")
    else:
        print("❌ Không thể lấy thông tin video")

def interactive_converter():
    """Converter tương tác"""
    downloader = YouTubeAudioDownloader()
    
    print("\n🔄 Interactive URL Converter")
    print("=" * 40)
    print("Nhập URL YouTube để convert (hoặc 'quit' để thoát)")
    
    while True:
        url = input("\nURL: ").strip()
        
        if url.lower() in ['quit', 'exit', 'q']:
            break
        
        if not url:
            continue
        
        print(f"\n🔍 Analyzing: {url}")
        
        # Analyze
        analysis = downloader.analyze_url_components(url)
        
        if not analysis['video_id']:
            print("❌ Không tìm thấy video ID")
            continue
        
        print(f"✅ Video ID: {analysis['video_id']}")
        
        # Show what will be removed
        removed = []
        if analysis['has_playlist']:
            removed.append("playlist")
        if analysis['has_radio']:
            removed.append("radio")
        if analysis['has_timestamp']:
            removed.append("timestamp")
        
        if removed:
            print(f"🗑️  Sẽ loại bỏ: {', '.join(removed)}")
        
        # Convert
        downloadable_url = analysis['downloadable_url']
        print(f"🎯 URL downloadable: {downloadable_url}")
        
        # Validate
        is_valid = downloader.is_valid_youtube_url(downloadable_url)
        print(f"✅ Valid: {'Yes' if is_valid else 'No'}")

def main():
    """Main function"""
    print("🎵 YouTube URL Converter Test")
    
    # Test all URLs
    test_url_converter()
    
    # Test specific URL
    test_specific_url()
    
    # Interactive mode
    try:
        interactive_converter()
    except KeyboardInterrupt:
        print("\n\n👋 Tạm biệt!")

if __name__ == "__main__":
    main()
