"""
Test Case Generator Chatbot Application
A Gradio-based application for generating comprehensive test cases
"""

import gradio as gr
import os
import tempfile
from typing import List, Dict, Any, Optional, Tuple

from project_manager import ProjectManager
from file_parser import FileParser
from test_case_generator import <PERSON><PERSON>aseGenerator
from output_handler import OutputHandler


class TestCaseGeneratorApp:
    """Main application class for the Test Case Generator"""
    
    def __init__(self):
        self.project_manager = ProjectManager()
        self.file_parser = FileParser()
        self.test_generator = TestCaseGenerator()
        self.output_handler = OutputHandler()
        
        # Application state
        self.current_project_id = None
        self.uploaded_files = []
        self.generated_test_cases = []
        self.output_files = {'txt': None, 'excel': None}

    def clean_project_name(self, project_name: str) -> str:
        """Clean project name by removing validation prefixes"""
        if not project_name:
            return project_name

        clean_name = project_name
        if project_name.startswith("✅ "):
            clean_name = project_name[2:].strip()
        elif project_name.startswith("⚠️ "):
            clean_name = project_name.split(" (")[0][2:].strip()

        return clean_name

    def get_project_input_files_display(self, project_name: str) -> str:
        """Get formatted display of input files only with management options"""
        if not project_name:
            return ""

        clean_project_name = self.clean_project_name(project_name)
        project = self.project_manager.get_project_by_name(clean_project_name)
        if not project:
            return ""

        project_id = project['id']
        files = self.project_manager.get_project_files(project_id)

        # Filter only input files
        input_files = [f for f in files if f.get('category') == 'input']

        if not input_files:
            return "📭 **No input files uploaded yet**\n*Upload requirement documents using the file upload component above*"

        display_lines = []
        display_lines.append(f"📂 **Input Files ({len(input_files)} file{'s' if len(input_files) != 1 else ''})**")
        display_lines.append("")

        for i, file_info in enumerate(input_files, 1):
            file_path = file_info.get('path', '')
            original_name = file_info.get('original_name', 'Unknown')
            file_type = file_info.get('file_type', '').upper()
            file_size = file_info.get('file_size', 0)

            # Format file size
            if file_size > 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            elif file_size > 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size} bytes"

            # Check if file exists
            file_exists = os.path.exists(file_path)
            status_icon = "✅" if file_exists else "❌"

            # File entry with details
            display_lines.append(f"**{i}. {original_name}**")
            display_lines.append(f"   • Type: {file_type} | Size: {size_str} | Status: {status_icon}")
            display_lines.append(f"   • Path: `{file_path}`")
            display_lines.append("")

        return "\n".join(display_lines)



    def get_project_inputs_root_path(self, project_name: str) -> str:
        """Get the inputs directory path for a project to use with FileExplorer"""
        if not project_name:
            return ""

        clean_project_name = self.clean_project_name(project_name)
        project = self.project_manager.get_project_by_name(clean_project_name)
        if not project:
            return ""

        project_id = project['id']
        project_root = self.project_manager.get_project_path(project_id)
        if not project_root:
            return ""

        inputs_path = os.path.join(project_root, "inputs")
        return inputs_path if os.path.exists(inputs_path) else ""

    def create_project(self, name: str, description: str) -> gr.Dropdown:
        """Create a new project"""
        try:
            if not name.strip():
                return gr.Dropdown(choices=self.get_project_choices(), value=None)

            project_id = self.project_manager.create_project(name.strip(), description.strip())
            self.current_project_id = project_id

            # Update project dropdown with validation indicators and set the new project as selected
            project_choices = self.get_all_project_names_with_validation()
            # Find the new project in the choices (it should be valid since it's new)
            selected_value = None
            for choice in project_choices:
                if choice.endswith(name.strip()) and choice.startswith("✅"):
                    selected_value = choice
                    break

            updated_dropdown = gr.Dropdown(choices=project_choices, value=selected_value)

            return updated_dropdown

        except ValueError as e:
            return gr.Dropdown(choices=self.get_project_choices(), value=None)
        except Exception as e:
            return gr.Dropdown(choices=self.get_project_choices(), value=None)
    
    def select_project(self, project_name: str) -> str:
        """Select an existing project with validation"""
        if not project_name:
            self.current_project_id = None
            return "⚠️ No project selected."

        # Handle project names with validation prefixes
        clean_project_name = self.clean_project_name(project_name)

        project = self.project_manager.get_project_by_name(clean_project_name)
        if not project:
            self.current_project_id = None
            return f"❌ Project '{clean_project_name}' not found."

        # Validate project files
        validation = self.validate_project_files(clean_project_name)

        if not validation['valid']:
            self.current_project_id = None
            return f"❌ Cannot select project '{clean_project_name}': {validation['reason']}. Please fix file issues first."

        # Project is valid, select it
        self.current_project_id = project['id']
        files_count = validation['file_count']
        valid_files = validation['valid_files']
        outputs_count = len([f for f in project.get('files', []) if f.get('category') == 'output'])

        return f"✅ Selected project '{clean_project_name}'. Contains {valid_files}/{files_count} valid file(s) and {outputs_count} generated output(s)."
    
    def upload_files(self, files) -> str:
        """Handle file uploads with project-specific storage"""
        if not self.current_project_id:
            return "❌ Please select or create a project before uploading files."

        if not files:
            return "❌ No files selected for upload."

        uploaded_count = 0
        error_messages = []

        # Get project-specific inputs directory
        project_inputs_path = self.project_manager.get_project_inputs_path(self.current_project_id)
        if not project_inputs_path:
            return "❌ Unable to determine project storage location."

        for file in files:
            try:
                if not self.file_parser.is_supported_file(file.name):
                    error_messages.append(f"File '{file.name}' format not supported.")
                    continue

                # Extract clean original filename
                original_filename = os.path.basename(file.name)

                # Save file to project-specific inputs directory
                saved_path, file_type = self.file_parser.save_uploaded_file(
                    file,
                    original_filename,
                    project_inputs_path
                )

                # Parse file content and extract metadata
                try:
                    content = self.file_parser.parse_file(saved_path, file_type)
                    file_metadata = self.file_parser.extract_requirements_info(content)
                except Exception as e:
                    print(f"Warning: Could not extract metadata from {original_filename}: {str(e)}")
                    file_metadata = {}

                # Add to project with enhanced metadata
                self.project_manager.add_file_to_project(
                    self.current_project_id,
                    saved_path,
                    original_filename,
                    file_type,
                    file_metadata
                )

                uploaded_count += 1

            except Exception as e:
                error_messages.append(f"Upload error for '{os.path.basename(file.name)}': {str(e)}")

        result_message = f"✅ Successfully uploaded {uploaded_count} file(s) to project storage."
        if error_messages:
            result_message += f"\n⚠️ Issues encountered:\n" + "\n".join(error_messages)

        return result_message
    
    def generate_test_cases(self, ai_model: str, custom_requirements: str = None) -> Tuple[str, str, str, str]:
        """Generate test cases from uploaded files"""
        if not self.current_project_id:
            return "❌ Please select a project first.", "", None, None

        project = self.project_manager.get_project(self.current_project_id)
        if not project:
            return "❌ Project not found.", "", None, None

        files = project.get('files', [])
        if not files:
            return "❌ No files uploaded. Please upload requirement documents first.", "", None, None
        
        try:
            # Get related file groups for intelligent processing
            file_groups = self.project_manager.get_related_files_groups(self.current_project_id)

            if not file_groups:
                return "❌ No input files found in project.", "", None, None

            # Enhanced content combination based on file relationships
            combined_content = self._create_intelligent_content_combination(file_groups)

            if not combined_content:
                return "❌ Unable to read content from uploaded files.", "", None, None

            # Combine all content with relationship context
            full_content = combined_content

            # Generate test cases using selected AI model
            selected_model = None if ai_model == "Mock (Built-in Generator)" else ai_model
            self.generated_test_cases = self.test_generator.generate_test_cases(full_content, selected_model, custom_requirements)

            # Mark input files as processed
            for file_info in files:
                if file_info.get('id'):
                    self.project_manager.update_file_processing_status(
                        self.current_project_id,
                        file_info['id'],
                        'processed'
                    )
            
            if not self.generated_test_cases:
                return "❌ Unable to generate test cases from file content.", "", None, None

            # Set project-specific output directory
            project_output_dir = self.project_manager.get_project_outputs_path(self.current_project_id)
            if project_output_dir:
                self.output_handler.set_project_output_directory(project_output_dir)

            # Generate output files with model information
            txt_path, excel_path = self.output_handler.generate_output_files(
                self.generated_test_cases,
                project['name'],
                selected_model if selected_model else "Mock"
            )

            # Save output file paths to project
            self.project_manager.add_generated_output(
                self.current_project_id,
                txt_path,
                excel_path,
                selected_model if selected_model else "Mock"
            )

            # Store for download
            self.output_files = {'txt': txt_path, 'excel': excel_path}

            # Format for display
            display_content = self.output_handler.format_test_cases_for_display(self.generated_test_cases)

            test_case_count = len([tc for tc in self.generated_test_cases if tc.get('ID', '').strip()])

            model_used = selected_model if selected_model else "Built-in Generator"

            return (
                f"✅ Successfully generated {test_case_count} professional test cases using {model_used}!",
                display_content,
                txt_path,
                excel_path
            )

        except Exception as e:
            return f"❌ Error generating test cases: {str(e)}", "", None, None
    
    def download_txt_file(self):
        """Return path to TXT file for download"""
        if self.output_files.get('txt') and os.path.exists(self.output_files['txt']):
            return self.output_files['txt']
        return None
    
    def download_excel_file(self):
        """Return path to Excel file for download"""
        if self.output_files.get('excel') and os.path.exists(self.output_files['excel']):
            return self.output_files['excel']
        return None
    
    def get_project_choices(self):
        """Get current project choices for dropdown with format validation"""
        return self.get_valid_project_names()

    def validate_project_files(self, project_name: str) -> dict:
        """Validate all files in a project and return validation results"""
        if not project_name:
            return {'valid': False, 'reason': 'No project name provided'}

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return {'valid': False, 'reason': 'Project not found'}

        project_id = project['id']
        files = self.project_manager.get_project_files(project_id)

        if not files:
            return {'valid': True, 'reason': 'Empty project (valid)', 'file_count': 0, 'valid_files': 0, 'invalid_files': [], 'missing_files': []}

        valid_files = 0
        invalid_files = []
        missing_files = []

        # Get project paths for validation
        project_root = self.project_manager.get_project_path(project_id)
        inputs_path = os.path.join(project_root, "inputs") if project_root else ""

        # Note: Output files are stored in global 'outputs/' directory, not project-specific directories
        global_outputs_path = "outputs"

        for file_info in files:
            file_path = file_info.get('path', '')
            original_name = file_info.get('original_name', 'Unknown')
            category = file_info.get('category', 'input')

            # Check if file exists
            if not os.path.exists(file_path):
                missing_files.append(original_name)
                continue

            # Check if file is in the correct directory based on category (more flexible path checking)
            if category == 'input':
                # Input files should be in project-specific inputs directory
                # Check both absolute and relative paths
                if inputs_path:
                    abs_inputs_path = os.path.abspath(inputs_path)
                    abs_file_path = os.path.abspath(file_path)
                    # More flexible path checking - allow if file is in inputs directory or subdirectory
                    if not (abs_file_path.startswith(abs_inputs_path) or
                           'inputs' in file_path or
                           file_path.startswith(inputs_path)):
                        # Only flag as invalid if it's clearly in wrong location
                        if not any(valid_dir in file_path for valid_dir in ['inputs', 'projects']):
                            invalid_files.append(f"{original_name} (not in inputs directory)")
                            continue
            elif category == 'output':
                # Output files should be in global outputs directory
                # More flexible checking for output files
                abs_file_path = os.path.abspath(file_path)
                abs_outputs_path = os.path.abspath(global_outputs_path)
                if not (abs_file_path.startswith(abs_outputs_path) or
                       'outputs' in file_path or
                       file_path.startswith(global_outputs_path)):
                    # Only flag as invalid if it's clearly in wrong location
                    if not any(valid_dir in file_path for valid_dir in ['outputs', 'projects']):
                        invalid_files.append(f"{original_name} (not in outputs directory)")
                        continue

            # Check if file format is supported for input files
            if category == 'input' and not self.file_parser.is_supported_file(original_name):
                invalid_files.append(f"{original_name} (unsupported format)")
                continue

            # For output files, check if they are legitimate test case files
            if category == 'output':
                file_type = file_info.get('file_type', '').lower()
                if file_type not in ['.txt', '.xlsx']:
                    invalid_files.append(f"{original_name} (invalid output format)")
                    continue

            valid_files += 1

        total_files = len(files)
        has_issues = len(invalid_files) > 0 or len(missing_files) > 0

        return {
            'valid': not has_issues,
            'reason': 'All files valid' if not has_issues else f'{len(invalid_files)} invalid format(s), {len(missing_files)} missing file(s)',
            'file_count': total_files,
            'valid_files': valid_files,
            'invalid_files': invalid_files,
            'missing_files': missing_files
        }

    def get_valid_project_names(self) -> List[str]:
        """Get list of project names that contain only valid file formats"""
        all_projects = self.project_manager.get_project_names()
        valid_projects = []

        for project_name in all_projects:
            validation = self.validate_project_files(project_name)
            if validation['valid']:
                valid_projects.append(project_name)

        return valid_projects

    def get_all_project_names_with_validation(self) -> List[str]:
        """Get all project names with validation status indicators"""
        all_projects = self.project_manager.get_project_names()
        project_choices = []

        for project_name in all_projects:
            validation = self.validate_project_files(project_name)
            if validation['valid']:
                project_choices.append(f"✅ {project_name}")
            else:
                project_choices.append(f"⚠️ {project_name} ({validation['reason']})")

        return project_choices

    def delete_project_with_confirmation(self, project_name: str, confirmation: bool) -> Tuple[str, gr.Dropdown]:
        """Delete a project with confirmation"""
        if not project_name:
            return "❌ No project selected for deletion.", gr.Dropdown(choices=self.get_all_project_names_with_validation(), value=None)

        if not confirmation:
            return "❌ Please confirm deletion by checking the confirmation box.", gr.Dropdown(choices=self.get_all_project_names_with_validation(), value=None)

        # Handle project names with validation prefixes
        clean_project_name = self.clean_project_name(project_name)

        project = self.project_manager.get_project_by_name(clean_project_name)
        if not project:
            return f"❌ Project '{clean_project_name}' not found.", gr.Dropdown(choices=self.get_all_project_names_with_validation(), value=None)

        try:
            # Clear current selection if deleting the active project
            if self.current_project_id == project['id']:
                self.current_project_id = None

            # Delete the project (this will handle file cleanup)
            self.project_manager.delete_project(project['id'])

            # Update dropdown choices
            updated_choices = self.get_all_project_names_with_validation()
            updated_dropdown = gr.Dropdown(choices=updated_choices, value=None)

            return f"✅ Successfully deleted project '{clean_project_name}' and all associated files.", updated_dropdown

        except Exception as e:
            return f"❌ Error deleting project '{clean_project_name}': {str(e)}", gr.Dropdown(choices=self.get_all_project_names_with_validation(), value=None)

    def delete_project_and_update_ui(self, project_name: str) -> Tuple[gr.Dropdown, gr.FileExplorer, gr.Button, gr.File, gr.Button]:
        """Delete project and update all UI components"""
        # Delete the project
        _, updated_dropdown = self.delete_current_project(project_name)

        # After deletion, no project is selected, so hide file explorer
        file_explorer = gr.FileExplorer(
            label="Project Files",
            height=400,
            interactive=True,
            root_dir="",
            visible=False  # Hide file explorer after deletion
        )

        # Hide delete button since no project is selected
        delete_btn = gr.Button(
            "🗑️ Delete Project",
            variant="stop",
            elem_classes=["danger-btn"],
            visible=False
        )

        # Disable file upload and generate button since no project is selected
        file_upload_component = gr.File(
            label="Upload Files (.txt, .docx, .xlsx, .xls)",
            file_count="multiple",
            file_types=[".txt", ".docx", ".xlsx", ".xls"],
            interactive=False
        )

        generate_button = gr.Button(
            "Generate Test Cases",
            variant="primary",
            size="lg",
            elem_classes=["primary-btn"],
            interactive=False
        )

        return updated_dropdown, file_explorer, delete_btn, file_upload_component, generate_button

    def get_project_deletion_info(self, project_name: str) -> str:
        """Get information about what will be deleted"""
        if not project_name:
            return "⚠️ No project selected."

        # Handle project names with validation prefixes
        clean_project_name = project_name
        if project_name.startswith("✅ "):
            clean_project_name = project_name[2:].strip()
        elif project_name.startswith("⚠️ "):
            clean_project_name = project_name.split(" (")[0][2:].strip()

        project = self.project_manager.get_project_by_name(clean_project_name)
        if not project:
            return f"❌ Project '{clean_project_name}' not found."

        project_id = project['id']
        files = self.project_manager.get_project_files(project_id)
        categorized = self.project_manager.get_project_files_by_category(project_id)
        stats = self.project_manager.get_file_stats(project_id)

        info_lines = []
        info_lines.append(f"🗑️ **Project Deletion Preview: {clean_project_name}**")
        info_lines.append("")
        info_lines.append("**⚠️ WARNING: This action cannot be undone!**")
        info_lines.append("")
        info_lines.append("**The following will be permanently deleted:**")
        info_lines.append(f"• Project metadata and configuration")
        info_lines.append(f"• **{stats['input_files']} input file(s)** (uploaded documents)")
        info_lines.append(f"• **{stats['output_files']} output file(s)** (generated test cases)")
        info_lines.append(f"• **Total storage:** {self.project_manager.format_file_size(stats['total_size'])}")
        info_lines.append("")

        if categorized['input']:
            info_lines.append("**📤 Input files to be deleted:**")
            for file_info in categorized['input']:
                info_lines.append(f"  • {file_info.get('original_name', 'Unknown')}")
            info_lines.append("")

        if categorized['output']:
            info_lines.append("**📋 Output files to be deleted:**")
            for file_info in categorized['output']:
                friendly_name = self.get_friendly_output_name(file_info, clean_project_name)
                info_lines.append(f"  • {friendly_name}")
            info_lines.append("")

        info_lines.append("**To proceed with deletion:**")
        info_lines.append("1. Check the confirmation box below")
        info_lines.append("2. Click the 'Delete Project' button")

        return "\n".join(info_lines)

    def clear_all_projects_for_testing(self) -> str:
        """Clear all projects for testing purposes - USE WITH CAUTION"""
        try:
            # Get all project IDs
            project_ids = list(self.project_manager.projects.keys())

            if not project_ids:
                return "✅ No projects to clear."

            # Delete all projects
            for project_id in project_ids:
                try:
                    self.project_manager.delete_project(project_id)
                except Exception as e:
                    print(f"Error deleting project {project_id}: {e}")

            # Clear current selection
            self.current_project_id = None

            return f"✅ Successfully cleared {len(project_ids)} project(s) for testing."

        except Exception as e:
            return f"❌ Error clearing projects: {str(e)}"

    def delete_current_project(self, project_name: str) -> Tuple[str, gr.Dropdown]:
        """Delete project with confirmation and update UI state"""
        if not project_name:
            return "❌ No project selected for deletion.", gr.Dropdown(choices=self.get_all_project_names_with_validation(), value=None)

        # Handle project names with validation prefixes
        clean_project_name = self.clean_project_name(project_name)

        project = self.project_manager.get_project_by_name(clean_project_name)
        if not project:
            return f"❌ Project '{clean_project_name}' not found.", gr.Dropdown(choices=self.get_all_project_names_with_validation(), value=None)

        try:
            # Clear current selection if deleting the active project
            if self.current_project_id == project['id']:
                self.current_project_id = None

            # Delete the project (this will handle file cleanup)
            self.project_manager.delete_project(project['id'])

            # Update dropdown choices
            updated_choices = self.get_all_project_names_with_validation()
            updated_dropdown = gr.Dropdown(choices=updated_choices, value=None)

            return f"✅ Successfully deleted project '{clean_project_name}' and all associated files.", updated_dropdown

        except Exception as e:
            return f"❌ Error deleting project '{clean_project_name}': {str(e)}", gr.Dropdown(choices=self.get_all_project_names_with_validation(), value=None)

    def get_file_type_icon(self, file_type: str) -> str:
        """Get icon for file type"""
        icons = {
            '.txt': '📄',
            '.docx': '📝',
            '.xlsx': '📊',
            '.xls': '📊',
            '.pdf': '📕'
        }
        return icons.get(file_type.lower(), '📄')

    def get_friendly_output_name(self, file_info: dict, project_name: str) -> str:
        """Get user-friendly name for output files"""
        original_name = file_info.get('original_name', 'Unknown')
        file_type = file_info.get('file_type', '')
        model_used = file_info.get('model_used', 'Unknown')

        if file_type == '.txt':
            return f"Test Cases - {project_name}.txt"
        elif file_type == '.xlsx':
            return f"Test Cases - {project_name}.xlsx"
        else:
            return original_name

    def get_file_management_data(self, project_name: str) -> str:
        """Get enhanced file management data with professional gallery-style display"""
        if not project_name:
            return "📂 **Professional File Management**\n\n⚠️ No project selected. Please choose a project from the dropdown above to view and manage files."

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return f"📂 **Professional File Management**\n\n❌ Project '{project_name}' not found."

        project_id = project['id']
        files = self.project_manager.get_project_files(project_id)
        categorized = self.project_manager.get_project_files_by_category(project_id)
        stats = self.project_manager.get_file_stats(project_id)

        if not files:
            return f"""📂 **Professional File Management - {project_name}**

🎯 **Project Overview**
• Status: Empty project - ready for file uploads
• Storage Location: `projects/{project_name}/inputs/`
• Supported Formats: .txt, .docx, .xlsx, .xls

💡 **Quick Start:**
1. Upload requirement documents using the file upload component above
2. Files will be automatically organized in project-specific folders
3. Generate test cases from uploaded requirements
4. Download results in multiple formats"""

        # Build enhanced file management display
        output = [f"📂 **Professional File Management - {project_name}**"]
        output.append("")
        output.append("🎯 **Project Statistics**")
        output.append(f"• **Total Files:** {stats['total_files']} files")
        output.append(f"• **Storage Used:** {self.project_manager.format_file_size(stats['total_size'])}")
        output.append(f"• **Input Files:** {stats['input_files']} documents")
        output.append(f"• **Generated Outputs:** {stats['output_files']} files")
        output.append("")

        # Enhanced Input Files Section
        input_count = len(categorized['input'])
        output.append("📤 **Input Documents & Requirements**")
        output.append(f"*{input_count} requirement document(s) uploaded*")
        output.append("")

        if categorized['input']:
            for i, file_info in enumerate(categorized['input'], 1):
                status_icon = "✅" if file_info.get('processing_status') == 'processed' else "⏳"
                file_type_icon = self.get_file_type_icon(file_info.get('file_type', ''))
                file_type_desc = self.get_file_type_description(file_info.get('file_type', ''))
                size_str = self.project_manager.format_file_size(file_info.get('file_size', 0))
                upload_time = self.format_datetime(file_info.get('uploaded_at', ''))
                file_id = file_info.get('id', '')
                original_name = file_info.get('original_name', 'Unknown')
                processing_status = file_info.get('processing_status', 'pending').title()

                # Professional file card display
                output.append(f"**{i}. {file_type_icon} {original_name}**")
                output.append(f"   • **Type:** {file_type_desc}")
                output.append(f"   • **Size:** {size_str}")
                output.append(f"   • **Uploaded:** {upload_time}")
                output.append(f"   • **Status:** {status_icon} {processing_status}")
                output.append(f"   • **Actions:** 🗑️ [Delete File](delete:{file_id}:{original_name})")
                output.append("")
        else:
            output.append("📭 **No input files uploaded yet**")
            output.append("*Upload requirement documents using the file upload component above*")
            output.append("")

        # Enhanced Output Files Section
        output_count = len(categorized['output'])
        output.append("📋 **Generated Test Cases & Outputs**")
        output.append(f"*{output_count} generated file(s) available for download*")
        output.append("")

        if categorized['output']:
            # Group output files by generation session
            output_groups = {}
            for file_info in categorized['output']:
                model_used = file_info.get('model_used', 'Unknown')
                generated_time = self.format_datetime(file_info.get('uploaded_at', ''))
                group_key = f"{model_used}_{generated_time}"

                if group_key not in output_groups:
                    output_groups[group_key] = {
                        'model': model_used,
                        'time': generated_time,
                        'files': []
                    }
                output_groups[group_key]['files'].append(file_info)

            # Display grouped outputs
            for group_idx, (group_key, group_data) in enumerate(output_groups.items(), 1):
                output.append(f"**Generation Session {group_idx} - {group_data['model']} Model**")
                output.append(f"   • **Generated:** {group_data['time']}")
                output.append(f"   • **Files:** {len(group_data['files'])} output file(s)")

                for file_info in group_data['files']:
                    file_type_icon = self.get_file_type_icon(file_info.get('file_type', ''))
                    file_type_desc = self.get_file_type_description(file_info.get('file_type', ''))
                    size_str = self.project_manager.format_file_size(file_info.get('file_size', 0))
                    file_id = file_info.get('id', '')
                    friendly_name = self.get_friendly_output_name(file_info, project_name)

                    output.append(f"     ├─ {file_type_icon} **{friendly_name}**")
                    output.append(f"        • Type: {file_type_desc} • Size: {size_str}")
                    output.append(f"        • Actions: 🗑️ [Delete File](delete:{file_id}:{friendly_name})")

                output.append("")
        else:
            output.append("📭 **No test cases generated yet**")
            output.append("*Generate test cases from uploaded requirements to see outputs here*")
            output.append("")

        # Professional footer with tips and shortcuts
        output.append("---")
        output.append("💡 **File Management Tips**")
        output.append("• **Status Icons:** ⏳ Pending Processing, ✅ Processed & Ready")
        output.append("• **Quick Actions:** Click 🗑️ to delete files (with confirmation)")
        output.append("• **File Organization:** Files are stored in `projects/{}/inputs/` and `projects/{}/outputs/`".format(project_name, project_name))
        output.append("• **Supported Formats:** Text (.txt), Word (.docx), Excel (.xlsx, .xls)")
        output.append("")
        output.append("🔄 **Refresh this view after uploading new files or generating test cases**")

        return "\n".join(output)

    def get_file_type_icon(self, file_type: str) -> str:
        """Get professional icon for file type"""
        icons = {
            '.txt': '📄',      # Text document
            '.docx': '📝',     # Word document
            '.xlsx': '📊',     # Excel spreadsheet
            '.xls': '📊',      # Legacy Excel
            '.pdf': '📕',      # PDF document
            '.csv': '📋',      # CSV file
            '.json': '🔧',     # JSON file
            '.xml': '🏷️',      # XML file
            '.md': '📖',       # Markdown
            '.zip': '📦',      # Archive
            '.rar': '📦',      # Archive
            '.7z': '📦'        # Archive
        }
        return icons.get(file_type.lower(), '📄')

    def get_file_type_description(self, file_type: str) -> str:
        """Get human-readable description for file type"""
        descriptions = {
            '.txt': 'Text Document',
            '.docx': 'Word Document',
            '.xlsx': 'Excel Spreadsheet',
            '.xls': 'Excel Spreadsheet (Legacy)',
            '.pdf': 'PDF Document',
            '.csv': 'CSV Data File',
            '.json': 'JSON Data File',
            '.xml': 'XML Document',
            '.md': 'Markdown Document',
            '.zip': 'ZIP Archive',
            '.rar': 'RAR Archive',
            '.7z': '7-Zip Archive'
        }
        return descriptions.get(file_type.lower(), 'Unknown File Type')

    def format_datetime(self, datetime_str: str) -> str:
        """Format datetime string for display"""
        if not datetime_str:
            return "Unknown"
        try:
            # Parse ISO format datetime
            from datetime import datetime
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return datetime_str.split('T')[0] if 'T' in datetime_str else datetime_str

    def get_friendly_output_name(self, file_info: dict, project_name: str) -> str:
        """Generate user-friendly name for output files"""
        file_type = file_info.get('file_type', '').lower()
        model_used = file_info.get('model_used', 'Mock')

        if file_type == '.txt':
            return f"{project_name} - Test Cases (Text Format) - {model_used}"
        elif file_type in ['.xlsx', '.xls']:
            return f"{project_name} - Test Cases (Excel Format) - {model_used}"
        else:
            return file_info.get('original_name', 'Unknown File')

    def parse_delete_action(self, file_listing_content: str) -> tuple:
        """Parse delete action from file listing content"""
        # Look for delete links in the format: [Delete File](delete:file_id:filename)
        import re
        delete_pattern = r'\[Delete File\]\(delete:([^:]+):([^)]+)\)'
        matches = re.findall(delete_pattern, file_listing_content)

        if matches:
            # Return the last clicked delete action
            file_id, filename = matches[-1]
            return file_id, filename

        return None, None

    def get_file_details_for_confirmation(self, project_name: str, file_id: str) -> str:
        """Get enhanced file details for deletion confirmation"""
        if not project_name or not file_id:
            return "❌ **Invalid Selection**\n\nUnable to identify the file for deletion. Please try again."

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return f"❌ **Project Not Found**\n\nProject '{project_name}' could not be located."

        files = self.project_manager.get_project_files(project['id'])
        for file_info in files:
            if file_info.get('id') == file_id:
                size_str = self.project_manager.format_file_size(file_info.get('file_size', 0))
                upload_time = self.format_datetime(file_info.get('uploaded_at', ''))
                file_type = file_info.get('file_type', 'Unknown')
                file_type_desc = self.get_file_type_description(file_type)
                file_type_icon = self.get_file_type_icon(file_type)
                category = file_info.get('category', 'Unknown')
                processing_status = file_info.get('processing_status', 'unknown')

                if category == 'output':
                    friendly_name = self.get_friendly_output_name(file_info, project_name)
                    model_used = file_info.get('model_used', 'Unknown')
                else:
                    friendly_name = file_info.get('original_name', 'Unknown')
                    model_used = None

                # Enhanced confirmation dialog
                confirmation_text = f"""🗑️ **Confirm File Deletion**

{file_type_icon} **File Information:**
• **Name:** {friendly_name}
• **Type:** {file_type_desc} ({file_type.upper()})
• **Category:** {category.title()} File
• **Size:** {size_str}
• **Date:** {upload_time}"""

                if category == 'input':
                    confirmation_text += f"""
• **Status:** {processing_status.title()}"""
                elif category == 'output' and model_used:
                    confirmation_text += f"""
• **Generated by:** {model_used} AI Model"""

                confirmation_text += f"""

⚠️ **IMPORTANT WARNING**
This action will **permanently delete** the file from:
• Project storage location
• File system (`projects/{project_name}/{'inputs' if category == 'input' else 'outputs'}/`)
• Project database records

❌ **This action cannot be undone!**

🤔 **Are you absolutely sure you want to delete this file?**"""

                return confirmation_text

        return "❌ **File Not Found**\n\nThe selected file could not be located in the project. It may have already been deleted."

    def delete_file(self, project_name: str, file_id: str) -> str:
        """Delete a file from project with enhanced feedback"""
        if not project_name or not file_id:
            return "❌ **Invalid Request**\n\nMissing required parameters for file deletion."

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return f"❌ **Project Not Found**\n\nProject '{project_name}' could not be located."

        # Get file details before deletion for comprehensive feedback
        files = self.project_manager.get_project_files(project['id'])
        deleted_file_info = None
        for file_info in files:
            if file_info.get('id') == file_id:
                deleted_file_info = file_info
                break

        if not deleted_file_info:
            return "❌ **File Not Found**\n\nThe selected file could not be located. It may have already been deleted."

        # Extract file details for feedback
        if deleted_file_info.get('category') == 'output':
            deleted_file_name = self.get_friendly_output_name(deleted_file_info, project_name)
        else:
            deleted_file_name = deleted_file_info.get('original_name', 'Unknown file')

        file_type_icon = self.get_file_type_icon(deleted_file_info.get('file_type', ''))
        file_size = self.project_manager.format_file_size(deleted_file_info.get('file_size', 0))
        category = deleted_file_info.get('category', 'unknown')

        try:
            success = self.project_manager.delete_file_from_project(project['id'], file_id)
            if success:
                return f"""✅ **File Successfully Deleted**

{file_type_icon} **Deleted File:** {deleted_file_name}
📏 **Size Freed:** {file_size}
📁 **Category:** {category.title()} file
🗂️ **Project:** {project_name}

The file has been permanently removed from:
• Project storage directory
• File system
• Database records

💡 **Tip:** Use the refresh button to update the file listing."""
            else:
                return f"""❌ **Deletion Failed**

{file_type_icon} **File:** {deleted_file_name}
🗂️ **Project:** {project_name}

The file could not be deleted. Possible reasons:
• File is currently in use by another process
• Insufficient permissions
• File has already been deleted

Please try again or contact support if the issue persists."""
        except Exception as e:
            return f"""❌ **Deletion Error**

{file_type_icon} **File:** {deleted_file_name}
🗂️ **Project:** {project_name}
⚠️ **Error:** {str(e)}

An unexpected error occurred during deletion. Please try again or contact support."""
    
    def get_project_files_display(self, project_name: str) -> str:
        """Get enhanced project files display with comprehensive folder information"""
        if not project_name:
            return "⚠️ No project selected."

        clean_project_name = self.clean_project_name(project_name)
        project = self.project_manager.get_project_by_name(clean_project_name)
        if not project:
            return f"❌ Project '{clean_project_name}' not found."

        project_id = project['id']

        # Use the enhanced project folder display
        enhanced_display = self.get_enhanced_project_folder_display(clean_project_name)

        # Add file validation information
        categorized_files = self.project_manager.get_project_files_by_category(project_id)
        input_files = categorized_files.get('input', [])

        if input_files:
            valid_input_files = []
            invalid_input_files = []

            for file_info in input_files:
                file_path = file_info.get('path', '')
                original_name = file_info.get('original_name', 'Unknown')

                # Check if file exists and has valid format
                if os.path.exists(file_path) and self.file_parser.is_supported_file(original_name):
                    valid_input_files.append(file_info)
                else:
                    invalid_input_files.append(file_info)

            # Add validation summary to the enhanced display
            validation_lines = []
            validation_lines.append("")
            validation_lines.append("✅ **File Validation Summary**")
            validation_lines.append(f"• **Valid Files:** {len(valid_input_files)} ready for processing")

            if invalid_input_files:
                validation_lines.append(f"• **Invalid Files:** {len(invalid_input_files)} need attention")
                validation_lines.append("")
                validation_lines.append("⚠️ **Files Requiring Attention:**")
                for file_info in invalid_input_files:
                    file_path = file_info.get('path', '')
                    original_name = file_info.get('original_name', 'Unknown')
                    if not os.path.exists(file_path):
                        validation_lines.append(f"  • ❌ **{original_name}** - File not found")
                    elif not self.file_parser.is_supported_file(original_name):
                        validation_lines.append(f"  • ⚠️ **{original_name}** - Unsupported file type")

            enhanced_display += "\n".join(validation_lines)

        return enhanced_display

    def get_project_file_paths(self, project_name: str) -> List[str]:
        """Get list of file paths for FileExplorer component"""
        if not project_name:
            return []

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return []

        project_id = project['id']
        files = self.project_manager.get_project_files(project_id)

        # Return actual file paths that exist on disk
        file_paths = []
        for file_info in files:
            file_path = file_info.get('path', '')
            if file_path and os.path.exists(file_path):
                file_paths.append(file_path)

        return file_paths

    def get_project_root_path(self, project_name: str) -> str:
        """Get the root directory path for a project"""
        if not project_name:
            return ""

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return ""

        project_id = project['id']
        return self.project_manager.get_project_path(project_id)

    def get_project_file_explorer_value(self, project_name: str) -> str:
        """Get the appropriate root directory for FileExplorer based on project selection"""
        if not project_name:
            return ""

        project_root = self.get_project_root_path(project_name)
        if project_root and os.path.exists(project_root):
            return project_root
        return ""

    def select_project_and_show_files(self, project_name: str) -> Tuple[str, str]:
        """Select project and return status and file display"""
        status = self.select_project(project_name)
        files_display = self.get_project_files_display(project_name)
        return status, files_display

    def select_project_with_explorer(self, project_name: str) -> Tuple[gr.FileExplorer, gr.Button, gr.File, gr.Button]:
        """Select project and update file explorer with instruction messages and delete button visibility"""
        # Handle empty or None project name
        if not project_name or project_name.strip() == "":
            self.current_project_id = None

            # Empty file explorer when no project is selected
            file_explorer = gr.FileExplorer(
                label="Input Files",
                height=300,
                interactive=False,
                root_dir="",
                visible=False  # Hide project information section when no project selected
            )

            delete_btn = gr.Button(
                "🗑️ Delete Project",
                variant="stop",
                elem_classes=["danger-btn"],
                visible=False
            )

            # Disable file upload and generate button when no project is selected
            file_upload_component = gr.File(
                label="Upload Files (.txt, .docx, .xlsx, .xls)",
                file_count="multiple",
                file_types=[".txt", ".docx", ".xlsx", ".xls"],
                interactive=False
            )

            generate_button = gr.Button(
                "Generate Test Cases",
                variant="primary",
                size="lg",
                elem_classes=["primary-btn"],
                interactive=False
            )

            return file_explorer, delete_btn, file_upload_component, generate_button

        # Clean the project name first (remove validation prefixes)
        clean_project_name = self.clean_project_name(project_name)

        # Select the project
        self.select_project(project_name)

        # Get project root path for file explorer using clean name
        project_root = self.get_project_root_path(clean_project_name)

        # Update file explorer based on project selection and file content
        if self.current_project_id and project_root and os.path.exists(project_root):
            # Ensure the project directories exist
            inputs_path = os.path.join(project_root, "inputs")
            outputs_path = os.path.join(project_root, "outputs")
            os.makedirs(inputs_path, exist_ok=True)
            os.makedirs(outputs_path, exist_ok=True)

            # Check if project has input files using clean name
            project = self.project_manager.get_project_by_name(clean_project_name)
            input_files = [f for f in project.get('files', []) if f.get('category') == 'input'] if project else []
            has_input_files = len(input_files) > 0

            # Project Information Display Logic:
            # Only show project information when project is selected AND contains input files
            input_files = [f for f in project.get('files', []) if f.get('category') == 'input']
            has_input_files = len(input_files) > 0

            if has_input_files:
                # Show only the inputs directory to focus on input files
                inputs_root = self.get_project_inputs_root_path(clean_project_name)
                file_explorer = gr.FileExplorer(
                    label="Input Files",
                    height=300,
                    interactive=False,
                    root_dir=inputs_root if inputs_root else project_root,
                    glob="**/*.txt,**/*.docx,**/*.xlsx,**/*.xls",  # Only show supported input file types
                    ignore_glob="**/.git/**,**/__pycache__/**,**/*.pyc,**/*.py,**/*.json,**/*.md,**/*.log,**/.*",
                    visible=True
                )
            else:
                # Project selected but no input files - leave project information section empty
                file_explorer = gr.FileExplorer(
                    label="Input Files",
                    height=300,
                    interactive=False,
                    root_dir="",
                    visible=False
                )

            # Show delete button for valid selected project (regardless of file content)
            delete_btn = gr.Button(
                "🗑️ Delete Project",
                variant="stop",
                elem_classes=["danger-btn"],
                visible=True
            )

            # Enable file upload when project is selected
            file_upload_component = gr.File(
                label="Upload Files (.txt, .docx, .xlsx, .xls)",
                file_count="multiple",
                file_types=[".txt", ".docx", ".xlsx", ".xls"],
                interactive=True
            )

            # Enable generate button only if project has input files
            generate_button = gr.Button(
                "Generate Test Cases",
                variant="primary",
                size="lg",
                elem_classes=["primary-btn"],
                interactive=has_input_files
            )
        else:
            # Invalid project - hide project information section
            file_explorer = gr.FileExplorer(
                label="Input Files",
                height=300,
                interactive=False,
                root_dir="",
                visible=False
            )

            delete_btn = gr.Button(
                "🗑️ Delete Project",
                variant="stop",
                elem_classes=["danger-btn"],
                visible=False
            )

            # Disable file upload and generate button for invalid project
            file_upload_component = gr.File(
                label="Upload Files (.txt, .docx, .xlsx, .xls)",
                file_count="multiple",
                file_types=[".txt", ".docx", ".xlsx", ".xls"],
                interactive=False
            )

            generate_button = gr.Button(
                "Generate Test Cases",
                variant="primary",
                size="lg",
                elem_classes=["primary-btn"],
                interactive=False
            )

        return file_explorer, delete_btn, file_upload_component, generate_button

    def select_project_with_simple_explorer(self, project_name: str) -> Tuple[gr.FileExplorer, gr.Button, gr.File, gr.Button]:
        """Select project and update file explorer with simple project file browsing"""
        # Handle empty or None project name
        if not project_name or project_name.strip() == "":
            self.current_project_id = None

            # Empty file explorer when no project is selected
            file_explorer = gr.FileExplorer(
                label="Project Files",
                height=400,
                interactive=True,
                root_dir="",
                visible=False  # Hide when no project selected
            )

            delete_btn = gr.Button(
                "🗑️ Delete Project",
                variant="stop",
                elem_classes=["danger-btn"],
                visible=False
            )

            # Disable file upload and generate button when no project is selected
            file_upload_component = gr.File(
                label="Upload Files (.txt, .docx, .xlsx, .xls)",
                file_count="multiple",
                file_types=[".txt", ".docx", ".xlsx", ".xls"],
                interactive=False
            )

            generate_button = gr.Button(
                "Generate Test Cases",
                variant="primary",
                size="lg",
                elem_classes=["primary-btn"],
                interactive=False
            )

            return file_explorer, delete_btn, file_upload_component, generate_button

        # Clean the project name first
        clean_project_name = self.clean_project_name(project_name)

        # Select the project
        self.select_project(project_name)

        # Get project root path for file explorer
        project_root = self.get_project_root_path(clean_project_name)

        # Update file explorer based on project selection
        if self.current_project_id and project_root and os.path.exists(project_root):
            # Ensure the project directories exist
            inputs_path = os.path.join(project_root, "inputs")
            outputs_path = os.path.join(project_root, "outputs")
            os.makedirs(inputs_path, exist_ok=True)
            os.makedirs(outputs_path, exist_ok=True)

            # Check if project has any files
            project = self.project_manager.get_project_by_name(clean_project_name)
            input_files = [f for f in project.get('files', []) if f.get('category') == 'input'] if project else []
            has_input_files = len(input_files) > 0

            if has_input_files or os.listdir(inputs_path) or os.listdir(outputs_path):
                # Show file explorer with project root to display both inputs and outputs
                file_explorer = gr.FileExplorer(
                    label="Project Files",
                    height=400,
                    interactive=True,
                    root_dir=project_root,
                    glob="**/*",  # Show all files, filter with ignore_glob
                    ignore_glob="**/.git/**,**/__pycache__/**,**/*.pyc,**/*.py,**/*.json,**/*.md,**/*.log,**/.*,**/.DS_Store",
                    visible=True
                )
            else:
                # Project selected but no files - hide file explorer
                file_explorer = gr.FileExplorer(
                    label="Project Files",
                    height=400,
                    interactive=True,
                    root_dir="",
                    visible=False
                )

            # Show delete button for valid selected project
            delete_btn = gr.Button(
                "🗑️ Delete Project",
                variant="stop",
                elem_classes=["danger-btn"],
                visible=True
            )

            # Enable file upload when project is selected
            file_upload_component = gr.File(
                label="Upload Files (.txt, .docx, .xlsx, .xls)",
                file_count="multiple",
                file_types=[".txt", ".docx", ".xlsx", ".xls"],
                interactive=True
            )

            # Enable generate button only if project has input files
            generate_button = gr.Button(
                "Generate Test Cases",
                variant="primary",
                size="lg",
                elem_classes=["primary-btn"],
                interactive=has_input_files
            )
        else:
            # Invalid project - hide file explorer
            file_explorer = gr.FileExplorer(
                label="Project Files",
                height=400,
                interactive=True,
                root_dir="",
                visible=False
            )

            delete_btn = gr.Button(
                "🗑️ Delete Project",
                variant="stop",
                elem_classes=["danger-btn"],
                visible=False
            )

            # Disable file upload and generate button for invalid project
            file_upload_component = gr.File(
                label="Upload Files (.txt, .docx, .xlsx, .xls)",
                file_count="multiple",
                file_types=[".txt", ".docx", ".xlsx", ".xls"],
                interactive=False
            )

            generate_button = gr.Button(
                "Generate Test Cases",
                variant="primary",
                size="lg",
                elem_classes=["primary-btn"],
                interactive=False
            )

        return file_explorer, delete_btn, file_upload_component, generate_button

    def upload_files_with_simple_explorer_update(self, files) -> Tuple[gr.FileExplorer, gr.Button, gr.File]:
        """Upload files and update simple file explorer"""
        self.upload_files(files)

        # Get current project to update file explorer
        if self.current_project_id:
            project = self.project_manager.get_project(self.current_project_id)
            if project:
                clean_project_name = project['name']
                project_root = self.get_project_root_path(clean_project_name)

                if project_root and os.path.exists(project_root):
                    # Ensure directories exist
                    inputs_path = os.path.join(project_root, "inputs")
                    outputs_path = os.path.join(project_root, "outputs")
                    os.makedirs(inputs_path, exist_ok=True)
                    os.makedirs(outputs_path, exist_ok=True)

                    # Check if project has input files
                    input_files = [f for f in project.get('files', []) if f.get('category') == 'input']
                    has_input_files = len(input_files) > 0

                    # Update file explorer to show project root with both inputs and outputs
                    file_explorer = gr.FileExplorer(
                        label="Project Files",
                        height=400,
                        interactive=True,
                        root_dir=project_root,
                        glob="**/*",  # Show all files, filter with ignore_glob
                        ignore_glob="**/.git/**,**/__pycache__/**,**/*.pyc,**/*.py,**/*.json,**/*.md,**/*.log,**/.*,**/.DS_Store",
                        visible=True
                    )

                    # Enable generate button if we have input files
                    generate_button = gr.Button(
                        "Generate Test Cases",
                        variant="primary",
                        size="lg",
                        elem_classes=["primary-btn"],
                        interactive=has_input_files
                    )

                    # Clear file upload component
                    file_upload_component = gr.File(
                        label="Upload Files (.txt, .docx, .xlsx, .xls)",
                        file_count="multiple",
                        file_types=[".txt", ".docx", ".xlsx", ".xls"],
                        interactive=True,
                        value=None
                    )

                    return file_explorer, generate_button, file_upload_component

        # Fallback for invalid state
        return (
            gr.FileExplorer(label="Project Files", height=400, interactive=True, root_dir="", visible=False),
            gr.Button("Generate Test Cases", variant="primary", size="lg", elem_classes=["primary-btn"], interactive=False),
            gr.File(label="Upload Files (.txt, .docx, .xlsx, .xls)", file_count="multiple", file_types=[".txt", ".docx", ".xlsx", ".xls"], interactive=True, value=None)
        )

    def upload_files_with_explorer_update(self, files) -> Tuple[gr.FileExplorer, gr.Button, gr.File]:
        """Upload files and update file explorer, then clear the file upload component"""
        self.upload_files(files)

        # Get current project to update file explorer
        if self.current_project_id:
            project = self.project_manager.get_project(self.current_project_id)
            if project:
                project_root = self.get_project_root_path(project['name'])
                if project_root and os.path.exists(project_root):
                    # Ensure directories exist and are accessible
                    inputs_path = os.path.join(project_root, "inputs")
                    outputs_path = os.path.join(project_root, "outputs")
                    os.makedirs(inputs_path, exist_ok=True)
                    os.makedirs(outputs_path, exist_ok=True)

                    # Check if project now has files after upload
                    has_files = len(project.get('files', [])) > 0

                    # Project Information Display Logic: Show file explorer only if project has input files
                    input_files = [f for f in project.get('files', []) if f.get('category') == 'input']
                    has_input_files = len(input_files) > 0

                    if has_input_files:
                        # Show only the inputs directory to focus on input files
                        inputs_root = self.get_project_inputs_root_path(project['name'])
                        file_explorer = gr.FileExplorer(
                            label="Input Files",
                            height=300,
                            interactive=False,
                            root_dir=inputs_root if inputs_root else project_root,
                            glob="**/*.txt,**/*.docx,**/*.xlsx,**/*.xls",  # Only show supported input file types
                            ignore_glob="**/.git/**,**/__pycache__/**,**/*.pyc,**/*.py,**/*.json,**/*.md,**/*.log,**/.*",
                            visible=True
                        )
                    else:
                        # No input files yet - keep project information section empty
                        file_explorer = gr.FileExplorer(
                            label="Input Files",
                            height=300,
                            interactive=False,
                            root_dir="",
                            visible=False
                        )

                    # Enable generate button since we now have files
                    generate_button = gr.Button(
                        "Generate Test Cases",
                        variant="primary",
                        size="lg",
                        elem_classes=["primary-btn"],
                        interactive=True
                    )

                    # Clear the file upload component after successful upload
                    cleared_file_upload = gr.File(
                        label="Upload Files (.txt, .docx, .xlsx, .xls)",
                        file_count="multiple",
                        file_types=[".txt", ".docx", ".xlsx", ".xls"],
                        interactive=True,
                        value=None  # Clear the uploaded files
                    )

                    return file_explorer, generate_button, cleared_file_upload

        # Return unchanged if no project selected - hide project information
        file_explorer = gr.FileExplorer(
            label="Input Files",
            height=300,
            interactive=False,
            root_dir="",
            visible=False
        )

        # Keep generate button disabled
        generate_button = gr.Button(
            "Generate Test Cases",
            variant="primary",
            size="lg",
            elem_classes=["primary-btn"],
            interactive=False
        )

        # Keep file upload as is if no project selected
        unchanged_file_upload = gr.File(
            label="Upload Files (.txt, .docx, .xlsx, .xls)",
            file_count="multiple",
            file_types=[".txt", ".docx", ".xlsx", ".xls"],
            interactive=False
        )

        return file_explorer, generate_button, unchanged_file_upload

    def upload_files_and_update_display(self, files) -> Tuple[str, str]:
        """Upload files and return status and updated file display"""
        upload_status = self.upload_files(files)

        # Get current project name to update file display
        if self.current_project_id:
            project = self.project_manager.get_project(self.current_project_id)
            if project:
                files_display = self.get_project_files_display(project['name'])
                return upload_status, files_display

        return upload_status, ""

    def _create_intelligent_content_combination(self, file_groups: List[List[Dict[str, Any]]]) -> str:
        """Create intelligent content combination based on file relationships"""
        combined_sections = []

        for group_idx, file_group in enumerate(file_groups, 1):
            if len(file_group) == 1:
                # Single file - treat as independent
                file_info = file_group[0]
                if os.path.exists(file_info['path']):
                    content = self.file_parser.parse_file(file_info['path'], file_info['file_type'])
                    combined_sections.append(f"=== File: {file_info['original_name']} ===\n{content}\n")
            else:
                # Multiple related files - create integrated section
                group_content = []
                group_content.append(f"=== Related Files Group {group_idx} ===")

                # Add context about file relationships
                file_types = [f.get('metadata', {}).get('file_type', 'unknown') for f in file_group]
                unique_types = list(set(file_types))

                if len(unique_types) > 1:
                    group_content.append(f"Integration Context: {', '.join(unique_types)}")

                # Add each file's content with clear separation
                for file_info in file_group:
                    if os.path.exists(file_info['path']):
                        content = self.file_parser.parse_file(file_info['path'], file_info['file_type'])
                        metadata = file_info.get('metadata', {})
                        file_type = metadata.get('file_type', 'unknown')

                        group_content.append(f"\n--- File: {file_info['original_name']} (Type: {file_type}) ---")

                        # Add file summary if available
                        if metadata.get('content_summary'):
                            group_content.append(f"Summary: {metadata['content_summary']}")

                        # Add key entities if available
                        if metadata.get('key_entities'):
                            group_content.append(f"Key Entities: {', '.join(metadata['key_entities'][:5])}")

                        group_content.append(content)

                combined_sections.append('\n'.join(group_content) + '\n')

        return '\n'.join(combined_sections)

    def generate_and_update_downloads(self, ai_model: str, custom_requirements: str = None) -> Tuple[str, str, gr.DownloadButton, gr.DownloadButton]:
        """Generate test cases and update download buttons"""
        status, display_content, txt_path, excel_path = self.generate_test_cases(ai_model, custom_requirements)

        # Update download buttons with file paths
        txt_btn = gr.DownloadButton(
            label="Download .txt",
            variant="secondary",
            elem_classes=["secondary-btn"],
            visible=True if txt_path else False,
            value=txt_path if txt_path else None
        )

        excel_btn = gr.DownloadButton(
            label="Download .xlsx",
            variant="secondary",
            elem_classes=["secondary-btn"],
            visible=True if excel_path else False,
            value=excel_path if excel_path else None
        )

        return status, display_content, txt_btn, excel_btn

    def generate_and_update_downloads_with_results(self, ai_model: str, custom_requirements: str = None) -> Tuple[str, gr.DownloadButton, gr.DownloadButton]:
        """Generate test cases and update download buttons"""
        status, display_content, txt_path, excel_path = self.generate_test_cases(ai_model, custom_requirements)

        # Update download buttons with file paths
        txt_btn = gr.DownloadButton(
            label="Download .txt",
            variant="secondary",
            elem_classes=["secondary-btn"],
            visible=True if txt_path else False,
            value=txt_path if txt_path else None
        )

        excel_btn = gr.DownloadButton(
            label="Download .xlsx",
            variant="secondary",
            elem_classes=["secondary-btn"],
            visible=True if excel_path else False,
            value=excel_path if excel_path else None
        )

        return display_content, txt_btn, excel_btn

    def create_interface(self):
        """Create the simplified Gradio interface"""
        with gr.Blocks(
            title="Professional Test Case Generator",
            theme=gr.themes.Soft(),
            css="""
            /* Professional Clean Design */
            .main-header {
                text-align: center;
                color: #1e3a8a;
                margin-bottom: 1.5rem;
                padding: 1.5rem;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 1px solid #cbd5e1;
                box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
            }
            .section-container {
                margin-bottom: 1.5rem;
                padding: 1.5rem;
                background: white;
                border-radius: 8px;
                border: 1px solid #e2e8f0;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
            .section-title {
                color: #1e40af;
                font-weight: 600;
                font-size: 1.1rem;
                margin-bottom: 1rem;
                border-bottom: 2px solid #3b82f6;
                padding-bottom: 0.5rem;
            }
            .section-header::before {
                content: '';
                position: absolute;
                left: 0;
                bottom: -3px;
                width: 60px;
                height: 3px;
                background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                border-radius: 2px;
            }
            .professional-card {
                background: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
                transition: all 0.2s ease-in-out;
            }
            .professional-card:hover {
                box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
                border-color: #cbd5e1;
            }
            .file-management-card {
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
            }
            .status-success {
                background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
                border: 1px solid #86efac;
                color: #166534;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
                font-weight: 500;
            }
            .status-error {
                background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
                border: 1px solid #fca5a5;
                color: #dc2626;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
                font-weight: 500;
            }
            .status-info {
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                border: 1px solid #93c5fd;
                color: #1d4ed8;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
                font-weight: 500;
            }
            .ai-status-card {
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border: 1px solid #cbd5e1;
                border-radius: 12px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            }
            .divider {
                height: 2px;
                background: linear-gradient(to right, transparent, #cbd5e1, transparent);
                margin: 2.5rem 0;
                border-radius: 1px;
            }
            /* Enhanced Button Styling */
            .primary-btn {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border: none;
                color: white;
                font-weight: 600;
                border-radius: 8px;
                transition: all 0.2s ease-in-out;
                box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.3);
            }
            .primary-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px 0 rgba(59, 130, 246, 0.4);
            }
            .secondary-btn {
                background: linear-gradient(135deg, #64748b 0%, #475569 100%);
                border: none;
                color: white;
                font-weight: 500;
                border-radius: 8px;
                transition: all 0.2s ease-in-out;
                box-shadow: 0 2px 4px 0 rgba(100, 116, 139, 0.3);
            }
            .secondary-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px 0 rgba(100, 116, 139, 0.4);
            }
            .danger-btn {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                border: none;
                color: white;
                font-weight: 600;
                border-radius: 8px;
                transition: all 0.2s ease-in-out;
                box-shadow: 0 2px 4px 0 rgba(239, 68, 68, 0.3);
            }
            .danger-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px 0 rgba(239, 68, 68, 0.4);
            }
            /* Responsive Design */
            @media (max-width: 768px) {
                .main-header {
                    padding: 1rem;
                    margin-bottom: 1rem;
                }
                .professional-card {
                    padding: 1rem;
                    margin: 0.5rem 0;
                }
                .section-header {
                    font-size: 1.1rem;
                    margin: 1.5rem 0 1rem 0;
                }
            }
            /* File Upload Enhancement */
            .file-upload-area {
                border: 2px dashed #cbd5e1;
                border-radius: 12px;
                padding: 2rem;
                text-align: center;
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                transition: all 0.2s ease-in-out;
            }
            .file-upload-area:hover {
                border-color: #3b82f6;
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            }
            .file-display {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 1rem;
                margin-top: 1rem;
                font-size: 0.9rem;
                line-height: 1.5;
            }
            """
        ) as interface:

            # Simplified Header
            gr.Markdown(
                """
                # 🚀 Professional Test Case Generator
                ### AI-Powered Vietnamese Test Case Generation

                **📁 Supported Files:** .txt, .docx, .xlsx, .xls | **🤖 AI Models:** ChatGPT, Gemini | **🌐 Output:** Test Cases
                """,
                elem_classes=["main-header"]
            )

            # Section 1: Project Management (Independent)
            with gr.Group(elem_classes=["section-container"]):
                gr.Markdown("## 📁 Project Management", elem_classes=["section-title"])

                with gr.Row():
                    with gr.Column(scale=1):
                        project_name_input = gr.Textbox(
                            label="Project Name",
                            placeholder="Enter project name...",
                            max_lines=1
                        )
                    with gr.Column(scale=1):
                        project_desc_input = gr.Textbox(
                            label="Project Description",
                            placeholder="Brief description...",
                            max_lines=1
                        )

                create_project_btn = gr.Button(
                    "Create Project",
                    variant="primary",
                    elem_classes=["primary-btn"]
                )

                project_dropdown = gr.Dropdown(
                    label="Select Existing Project",
                    choices=self.get_all_project_names_with_validation(),
                    interactive=True,
                    value=None
                )



                # Project Information Section - Empty by default, only shows content when project has files
                with gr.Group() as project_info_group:
                    # No title or content shown initially - completely empty section
                    project_file_explorer = gr.FileExplorer(
                        label="Project Files",
                        height=400,
                        interactive=True,
                        root_dir="",
                        glob="**/*",  # Show all files, then filter with ignore_glob
                        ignore_glob="**/.git/**,**/__pycache__/**,**/*.pyc,**/*.py,**/*.json,**/*.md,**/*.log,**/.*,**/.DS_Store",
                        visible=False  # Initially hidden, will be shown only when project has files
                    )

                # Delete Project Button - Always positioned below Project Information, visible when project selected
                delete_project_btn = gr.Button(
                    "🗑️ Delete Project",
                    variant="stop",
                    elem_classes=["danger-btn"],
                    visible=False
                )

            # Section 2: File Upload (Always Visible)
            with gr.Group(elem_classes=["section-container"]) as file_upload_group:
                gr.Markdown("## 📤 File Upload", elem_classes=["section-title"])

                file_upload = gr.File(
                    label="Upload Files (.txt, .docx, .xlsx, .xls)",
                    file_count="multiple",
                    file_types=[".txt", ".docx", ".xlsx", ".xls"],
                    interactive=False  # Initially disabled until project is selected
                )



            # Section 3: Test Case Generation (Always Visible)
            with gr.Group(elem_classes=["section-container"]) as generation_group:
                gr.Markdown("## 🤖 Test Case Generation", elem_classes=["section-title"])



                ai_model_dropdown = gr.Dropdown(
                    label="AI Model Selection",
                    choices=self.test_generator.get_available_ai_models(),
                    value=self.test_generator.get_available_ai_models()[0],
                    interactive=True
                )

                custom_requirements_input = gr.Textbox(
                    label="Additional Requirements",
                    placeholder="Enter any specific testing requirements or focus areas...",
                    lines=3,
                    max_lines=5,
                    interactive=True,
                    info="Optional: Add special requirements, specific test scenarios, or areas that need focused attention"
                )

                generate_btn = gr.Button(
                    "Generate Test Cases",
                    variant="primary",
                    size="lg",
                    elem_classes=["primary-btn"],
                    interactive=False  # Initially disabled until project has files
                )



            # Section 4: View and Download Test Cases (Always Visible)
            with gr.Group(elem_classes=["section-container"]) as results_group:
                gr.Markdown("## 📋 Generated Test Cases", elem_classes=["section-title"])

                test_cases_output = gr.Textbox(
                    label="Test Cases",
                    lines=20,
                    max_lines=30,
                    interactive=False,
                    show_copy_button=True,
                    placeholder="Generated test cases will appear here..."
                )

                with gr.Row():
                    download_txt_btn = gr.DownloadButton(
                        label="Download .txt",
                        variant="secondary",
                        elem_classes=["secondary-btn"],
                        visible=False
                    )
                    download_excel_btn = gr.DownloadButton(
                        label="Download .xlsx",
                        variant="secondary",
                        elem_classes=["secondary-btn"],
                        visible=False
                    )

            # Event handlers
            create_project_btn.click(
                fn=self.create_project,
                inputs=[project_name_input, project_desc_input],
                outputs=[project_dropdown]
            )

            project_dropdown.change(
                fn=self.select_project_with_simple_explorer,
                inputs=[project_dropdown],
                outputs=[project_file_explorer, delete_project_btn, file_upload, generate_btn]
            )

            delete_project_btn.click(
                fn=self.delete_project_and_update_ui,
                inputs=[project_dropdown],
                outputs=[project_dropdown, project_file_explorer, delete_project_btn, file_upload, generate_btn]
            )

            file_upload.upload(
                fn=self.upload_files_with_simple_explorer_update,
                inputs=[file_upload],
                outputs=[project_file_explorer, generate_btn, file_upload]
            )

            generate_btn.click(
                fn=self.generate_and_update_downloads_with_results,
                inputs=[ai_model_dropdown, custom_requirements_input],
                outputs=[test_cases_output, download_txt_btn, download_excel_btn]
            )

        return interface


def main():
    """Main function to run the application"""
    app = TestCaseGeneratorApp()
    interface = app.create_interface()

    # Launch the application
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True,
        inbrowser=True
    )


if __name__ == "__main__":
    main()
