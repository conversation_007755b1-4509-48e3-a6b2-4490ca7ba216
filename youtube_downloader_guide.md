# YouTube Audio Downloader - Hướng dẫn sử dụng

## Cài đặt

### 1. Cài đặt Python packages
```bash
pip install yt-dlp ffmpeg-python
```

### 2. Cài đặt FFmpeg (cần thiết để chuyển đổi audio)

**Windows:**
- Tải FFmpeg từ https://ffmpeg.org/download.html
- Giải nén và thêm vào PATH

**macOS:**
```bash
brew install ffmpeg
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ffmpeg
```

**Conda (tất cả OS):**
```bash
conda install -c conda-forge ffmpeg
```

## Cách sử dụng

### 1. Sử dụng từ command line

**Cơ bản:**
```bash
python youtube_audio_downloader.py "https://www.youtube.com/watch?v=VIDEO_ID"
```

**Với tùy chọn:**
```bash
# Chỉ định thư mục output
python youtube_audio_downloader.py "URL" -o "my_music"

# Chỉ định tên file
python youtube_audio_downloader.py "URL" -n "my_song"

# Kết hợp cả hai
python youtube_audio_downloader.py "URL" -o "downloads" -n "favorite_song"
```

### 2. Sử dụng trong Python code

```python
from youtube_audio_downloader import YouTubeAudioDownloader

# Tạo downloader
downloader = YouTubeAudioDownloader("downloads")

# Tải một audio
url = "https://www.youtube.com/watch?v=VIDEO_ID"
success = downloader.download_audio(url)

# Tải với tên file tùy chỉnh
success = downloader.download_audio(url, "my_favorite_song")

# Tải nhiều audio
urls = [
    "https://www.youtube.com/watch?v=VIDEO_ID1",
    "https://www.youtube.com/watch?v=VIDEO_ID2"
]
results = downloader.download_multiple(urls)

# Kiểm tra thông tin video trước khi tải
info = downloader.get_video_info(url)
if info:
    print(f"Tiêu đề: {info['title']}")
    print(f"Thời lượng: {info['duration']} giây")
```

### 3. Demo tương tác

Chạy file mà không có tham số để sử dụng chế độ tương tác:
```bash
python youtube_audio_downloader.py
```

## Các tính năng

- ✅ Tải audio chất lượng cao (192kbps MP3)
- ✅ Hỗ trợ nhiều định dạng URL YouTube
- ✅ Tự động tạo thư mục output
- ✅ Hiển thị thông tin video (tiêu đề, kênh, thời lượng)
- ✅ Tùy chỉnh tên file
- ✅ Tải nhiều video cùng lúc
- ✅ Xử lý lỗi và thông báo rõ ràng
- ✅ Làm sạch tên file (loại bỏ ký tự đặc biệt)

## Định dạng URL được hỗ trợ

- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://www.youtube.com/embed/VIDEO_ID`
- `https://www.youtube.com/v/VIDEO_ID`

## Lưu ý

1. **Bản quyền**: Chỉ tải nội dung mà bạn có quyền sử dụng
2. **Chất lượng**: Audio được tải ở định dạng MP3 192kbps
3. **Thư mục**: File sẽ được lưu trong thư mục `downloads` (mặc định)
4. **FFmpeg**: Cần cài đặt FFmpeg để chuyển đổi audio
5. **Kết nối**: Cần kết nối internet để tải

## Xử lý lỗi thường gặp

**Lỗi: "yt-dlp not found"**
```bash
pip install yt-dlp
```

**Lỗi: "ffmpeg not found"**
- Cài đặt FFmpeg theo hướng dẫn ở trên

**Lỗi: "URL không hợp lệ"**
- Kiểm tra lại URL YouTube
- Đảm bảo video không bị private/deleted

**Lỗi: "Permission denied"**
- Kiểm tra quyền ghi vào thư mục output
- Chạy với quyền admin nếu cần

## Ví dụ hoàn chỉnh

```python
#!/usr/bin/env python3

from youtube_audio_downloader import YouTubeAudioDownloader
import os

def main():
    # Tạo downloader với thư mục tùy chỉnh
    music_dir = os.path.expanduser("~/Music/YouTube")
    downloader = YouTubeAudioDownloader(music_dir)
    
    # Danh sách các bài hát muốn tải
    songs = [
        {
            "url": "https://www.youtube.com/watch?v=VIDEO_ID1",
            "name": "Bài hát 1"
        },
        {
            "url": "https://www.youtube.com/watch?v=VIDEO_ID2", 
            "name": "Bài hát 2"
        }
    ]
    
    # Tải từng bài
    for song in songs:
        print(f"\nĐang tải: {song['name']}")
        success = downloader.download_audio(song["url"], song["name"])
        
        if success:
            print(f"✅ Đã tải xong: {song['name']}")
        else:
            print(f"❌ Lỗi khi tải: {song['name']}")

if __name__ == "__main__":
    main()
```
