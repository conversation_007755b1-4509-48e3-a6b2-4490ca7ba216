#!/usr/bin/env python3
"""
Test script for image_resizer.py
"""

import cv2
import numpy as np
import os
from image_resizer import add_morning_vibes_text, resize_image

def create_test_image():
    """Tạo ảnh test đơn giản"""
    # Tạo ảnh gradient đẹp mắt
    height, width = 1080, 1920
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Tạo gradient từ xanh dương đến cam (morning vibes)
    for y in range(height):
        for x in range(width):
            # Gradient từ trên xuống dưới
            ratio_y = y / height
            ratio_x = x / width
            
            # Màu xanh dương ở trên, cam ở dưới
            blue = int(135 * (1 - ratio_y) + 255 * ratio_x * 0.3)
            green = int(206 * (1 - ratio_y) + 165 * ratio_y)
            red = int(235 * ratio_y + 100 * (1 - ratio_y))
            
            image[y, x] = [blue, green, red]
    
    return image

def test_text_function():
    """Test chức năng thêm text"""
    print("🧪 Testing text function...")
    
    # Tạo ảnh test
    test_image = create_test_image()
    
    # Thêm text
    result_image = add_morning_vibes_text(test_image.copy())
    
    # Lưu ảnh test
    cv2.imwrite("test_with_text.jpg", result_image)
    print("✅ Đã tạo test_with_text.jpg")
    
    return True

def test_resize_function():
    """Test chức năng resize + text"""
    print("🧪 Testing resize + text function...")
    
    # Tạo và lưu ảnh test gốc
    test_image = create_test_image()
    cv2.imwrite("test_original.jpg", test_image)
    print("✅ Đã tạo test_original.jpg (1920x1080)")
    
    # Test resize với text
    success = resize_image("test_original.jpg", "test_resized_with_text.jpg", add_text=True)
    if success:
        print("✅ Đã tạo test_resized_with_text.jpg (1280x720 + Morning Vibes)")
    
    # Test resize không có text
    success = resize_image("test_original.jpg", "test_resized_no_text.jpg", add_text=False)
    if success:
        print("✅ Đã tạo test_resized_no_text.jpg (1280x720 only)")
    
    return True

def cleanup_test_files():
    """Dọn dẹp file test"""
    test_files = [
        "test_original.jpg",
        "test_with_text.jpg", 
        "test_resized_with_text.jpg",
        "test_resized_no_text.jpg"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️  Đã xóa {file}")

if __name__ == "__main__":
    print("🧪 TESTING IMAGE RESIZER WITH MORNING VIBES")
    print("=" * 50)
    
    try:
        # Test các chức năng
        test_text_function()
        print()
        test_resize_function()
        
        print("\n🎉 Tất cả test đều thành công!")
        print("📁 Kiểm tra các file được tạo:")
        print("   - test_with_text.jpg (ảnh gốc + text)")
        print("   - test_resized_with_text.jpg (1280x720 + text)")
        print("   - test_resized_no_text.jpg (1280x720 only)")
        
        # Hỏi có muốn dọn dẹp không
        response = input("\n🗑️  Có muốn xóa các file test không? (y/n): ")
        if response.lower() in ['y', 'yes']:
            cleanup_test_files()
        else:
            print("📁 Các file test được giữ lại để bạn kiểm tra")
            
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {str(e)}")
        import traceback
        traceback.print_exc()
