#!/usr/bin/env python3
"""
YouTube URL Converter - Convert raw URLs to downloadable URLs
Chuyển đổi URL YouTube raw (có playlist, radio, timestamp) thành URL download được
"""

import re
import sys
from urllib.parse import urlparse, parse_qs
from typing import Dict, Any, List

class YouTubeURLConverter:
    """Class chuyển đổi URL YouTube"""
    
    def __init__(self):
        pass
    
    def clean_url(self, url: str) -> str:
        """
        Làm sạch URL - loại bỏ escape characters
        
        Args:
            url: URL cần làm sạch
            
        Returns:
            URL đã được làm sạch
        """
        if not url:
            return url
            
        # Loại bỏ escape characters
        url = url.replace('\\?', '?').replace('\\=', '=').replace('\\&', '&')
        
        # Loại bỏ khoảng trắng
        url = url.strip()
        
        return url
    
    def extract_video_id(self, url: str) -> str:
        """
        Trích xuất video ID từ URL YouTube
        
        Args:
            url: URL YouTube
            
        Returns:
            Video ID hoặc chuỗi rỗng nếu không tìm thấy
        """
        # Các pattern để tìm video ID
        patterns = [
            r'(?:v=|/)([0-9A-Za-z_-]{11})',  # Standard format
            r'youtu\.be/([0-9A-Za-z_-]{11})',  # Short format
            r'embed/([0-9A-Za-z_-]{11})',  # Embed format
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return ""
    
    def analyze_url(self, url: str) -> Dict[str, Any]:
        """
        Phân tích URL YouTube và trích xuất thông tin
        
        Args:
            url: URL YouTube
            
        Returns:
            Dictionary chứa thông tin phân tích
        """
        clean_url = self.clean_url(url)
        parsed = urlparse(clean_url)
        query_params = parse_qs(parsed.query)
        
        analysis = {
            'original_url': url,
            'clean_url': clean_url,
            'domain': parsed.netloc,
            'path': parsed.path,
            'video_id': self.extract_video_id(clean_url),
            'parameters': {},
            'removed_params': [],
            'flags': {
                'has_playlist': False,
                'has_timestamp': False,
                'has_radio': False,
                'has_index': False,
                'has_feature': False,
                'is_short_url': 'youtu.be' in parsed.netloc,
                'is_mobile': 'm.youtube.com' in parsed.netloc,
                'is_embed': '/embed/' in parsed.path,
            },
            'downloadable_url': '',
            'is_valid': False
        }
        
        # Phân tích các tham số
        for key, values in query_params.items():
            value = values[0] if values else ''
            analysis['parameters'][key] = value
            
            # Phân loại tham số
            if key == 'list':
                analysis['flags']['has_playlist'] = True
                analysis['playlist_id'] = value
                analysis['removed_params'].append(f'playlist ({value})')
            elif key == 't':
                analysis['flags']['has_timestamp'] = True
                analysis['timestamp'] = value
                analysis['removed_params'].append(f'timestamp ({value})')
            elif key == 'start_radio':
                analysis['flags']['has_radio'] = True
                analysis['removed_params'].append('radio mode')
            elif key == 'index':
                analysis['flags']['has_index'] = True
                analysis['removed_params'].append(f'playlist index ({value})')
            elif key == 'feature':
                analysis['flags']['has_feature'] = True
                analysis['removed_params'].append(f'feature ({value})')
        
        # Tạo URL downloadable
        if analysis['video_id']:
            analysis['downloadable_url'] = f"https://www.youtube.com/watch?v={analysis['video_id']}"
            analysis['is_valid'] = True
        
        return analysis
    
    def convert_url(self, url: str) -> str:
        """
        Convert URL raw thành URL downloadable
        
        Args:
            url: URL YouTube raw
            
        Returns:
            URL clean có thể download
        """
        analysis = self.analyze_url(url)
        return analysis['downloadable_url'] if analysis['is_valid'] else url
    
    def batch_convert(self, urls: List[str]) -> List[Dict[str, str]]:
        """
        Convert nhiều URLs cùng lúc
        
        Args:
            urls: Danh sách URLs
            
        Returns:
            List các dictionary chứa kết quả convert
        """
        results = []
        
        for url in urls:
            analysis = self.analyze_url(url)
            results.append({
                'original': url,
                'converted': analysis['downloadable_url'],
                'video_id': analysis['video_id'],
                'valid': analysis['is_valid'],
                'removed': ', '.join(analysis['removed_params']) if analysis['removed_params'] else 'None'
            })
        
        return results
    
    def print_analysis(self, url: str):
        """
        In ra phân tích chi tiết của URL
        
        Args:
            url: URL cần phân tích
        """
        analysis = self.analyze_url(url)
        
        print(f"🔍 Phân tích URL:")
        print(f"   Original: {analysis['original_url']}")
        print(f"   Clean: {analysis['clean_url']}")
        print(f"   Video ID: {analysis['video_id']}")
        print(f"   Domain: {analysis['domain']}")
        
        # Flags
        flags = []
        if analysis['flags']['is_short_url']:
            flags.append('Short URL')
        if analysis['flags']['is_mobile']:
            flags.append('Mobile')
        if analysis['flags']['is_embed']:
            flags.append('Embed')
        
        if flags:
            print(f"   Type: {', '.join(flags)}")
        
        # Parameters
        if analysis['parameters']:
            print(f"   Parameters:")
            for key, value in analysis['parameters'].items():
                print(f"     - {key}: {value}")
        
        # What will be removed
        if analysis['removed_params']:
            print(f"   🗑️  Will remove: {', '.join(analysis['removed_params'])}")
        
        # Result
        print(f"   ✅ Downloadable: {analysis['downloadable_url']}")
        print(f"   Valid: {'Yes' if analysis['is_valid'] else 'No'}")


def main():
    """Main function"""
    converter = YouTubeURLConverter()
    
    if len(sys.argv) > 1:
        # Command line mode
        url = sys.argv[1]
        
        print("🔄 YouTube URL Converter")
        print("=" * 50)
        
        converter.print_analysis(url)
        
        converted = converter.convert_url(url)
        print(f"\n📋 Result:")
        print(f"   Input:  {url}")
        print(f"   Output: {converted}")
        
    else:
        # Interactive mode
        print("🔄 YouTube URL Converter")
        print("=" * 40)
        print("Convert raw YouTube URLs to downloadable URLs")
        print("Removes playlist, radio, timestamp, and other parameters")
        print("\nCommands:")
        print("  - Enter URL to convert")
        print("  - 'batch' for batch mode")
        print("  - 'quit' to exit")
        
        while True:
            print("\n" + "-" * 40)
            command = input("URL or command: ").strip()
            
            if command.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif command.lower() == 'batch':
                print("\nBatch mode - Enter URLs (empty line to finish):")
                urls = []
                while True:
                    url = input(f"URL {len(urls)+1}: ").strip()
                    if not url:
                        break
                    urls.append(url)
                
                if urls:
                    print(f"\n📊 Converting {len(urls)} URLs...")
                    results = converter.batch_convert(urls)
                    
                    print("\n📋 Results:")
                    for i, result in enumerate(results, 1):
                        print(f"\n{i}. {result['original']}")
                        print(f"   → {result['converted']}")
                        print(f"   Video ID: {result['video_id']}")
                        print(f"   Removed: {result['removed']}")
                        print(f"   Valid: {'✅' if result['valid'] else '❌'}")
            elif command:
                converter.print_analysis(command)
            else:
                print("❌ Please enter a URL or command")


if __name__ == "__main__":
    main()
