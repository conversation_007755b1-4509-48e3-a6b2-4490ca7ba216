#!/usr/bin/env python3
"""
Quick YouTube Audio Download - Demo đơn giản
"""

from youtube_audio_downloader import YouTubeAudioDownloader

def main():
    print("🎵 Quick YouTube Audio Downloader")
    print("="*40)
    
    # Tạo downloader
    downloader = YouTubeAudioDownloader("downloads")
    
    while True:
        print("\nNhập URL YouTube (hoặc 'quit' để thoát):")
        url = input("URL: ").strip()
        
        if url.lower() in ['quit', 'exit', 'q', '']:
            print("👋 Tạm biệt!")
            break
        
        print(f"\n🔍 Kiểm tra URL: {url}")
        
        # Kiểm tra URL
        if not downloader.is_valid_youtube_url(url):
            print("❌ URL không hợp lệ!")
            print("📝 Ví dụ URL đúng: https://www.youtube.com/watch?v=VIDEO_ID")
            continue
        
        print("✅ URL hợp lệ!")
        
        # Lấy thông tin video
        print("📋 Đang lấy thông tin video...")
        info = downloader.get_video_info(url)
        
        if not info:
            print("❌ Không thể lấy thông tin video. C<PERSON> thể video bị private hoặc không tồn tại.")
            continue
        
        # Hiển thị thông tin
        print(f"📺 Tiêu đề: {info['title']}")
        print(f"👤 Kênh: {info['uploader']}")
        duration_min = info['duration'] // 60
        duration_sec = info['duration'] % 60
        print(f"⏱️  Thời lượng: {duration_min}:{duration_sec:02d}")
        
        # Hỏi có muốn tải không
        download = input("\n💾 Tải audio? (y/n): ").strip().lower()
        
        if download in ['y', 'yes', 'có', '1']:
            # Hỏi tên file tùy chỉnh
            custom_name = input("📝 Tên file tùy chỉnh (Enter để dùng tên gốc): ").strip()
            
            print("\n🎵 Đang tải audio...")
            success = downloader.download_audio(
                url, 
                custom_name if custom_name else None,
                debug=True
            )
            
            if success:
                print("🎉 Tải thành công!")
                print(f"📁 File được lưu trong thư mục: downloads/")
            else:
                print("😞 Tải thất bại!")
        else:
            print("⏭️  Bỏ qua tải file.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Tạm biệt!")
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        print("💡 Hãy đảm bảo đã cài đặt: pip install yt-dlp ffmpeg-python")
