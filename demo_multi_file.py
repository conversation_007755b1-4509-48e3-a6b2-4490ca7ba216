#!/usr/bin/env python3
"""
Demo script to show multi-file test case generation
"""

import os
from ai_service import AIService

def demo_multi_file_generation():
    """Demo the complete multi-file test case generation"""
    
    print("=== DEMO: MULTI-FILE TEST CASE GENERATION ===\n")
    
    # Create AI service instance
    ai_service = AIService()
    
    # Check available models
    available_models = ai_service.get_available_models()
    print(f"Available AI models: {available_models}")
    
    if not available_models:
        print("No AI models available. Please configure API keys in .env file.")
        return
    
    # Demo content: Two related files (User management system)
    related_files_content = """=== File: user_api_spec.txt ===
API Specification - User Management System

Endpoints:
1. POST /api/users
   - Tạo người dùng mới
   - Input: name, email, password, role
   - Validation: email unique, password min 8 chars
   - Output: user object with ID

2. GET /api/users/{id}
   - <PERSON><PERSON><PERSON> thông tin người dùng theo ID
   - Authorization: required
   - Output: user object hoặc 404 error

3. PUT /api/users/{id}
   - Cậ<PERSON> nhật thông tin người dùng
   - Authorization: owner hoặc admin
   - Input: name, email, role (optional)
   - Output: updated user object

4. DELETE /api/users/{id}
   - Xóa người dùng
   - Authorization: admin only
   - Output: success message

=== File: user_ui_requirements.txt ===
User Interface Requirements - User Management

Màn hình danh sách người dùng:
- Hiển thị bảng với các cột: ID, Tên, Email, Vai trò, Ngày tạo
- Phân trang: 20 users per page
- Tìm kiếm theo tên hoặc email
- Filter theo vai trò (Admin, User, Guest)
- Nút "Thêm người dùng mới"

Form thêm/sửa người dùng:
- Trường bắt buộc: Tên, Email, Mật khẩu (chỉ khi thêm mới)
- Dropdown chọn vai trò
- Validation real-time
- Nút Lưu và Hủy
- Hiển thị thông báo thành công/lỗi

Chức năng xóa:
- Popup xác nhận trước khi xóa
- Không cho phép xóa chính mình
- Chỉ admin mới được xóa user khác"""
    
    print("Content to analyze:")
    print("=" * 50)
    print(related_files_content[:500] + "...")
    print("=" * 50)
    print()
    
    # Generate test cases using the first available model
    model = available_models[0]
    print(f"Generating test cases using {model}...")
    
    try:
        # Generate test cases
        result = ai_service.generate_test_cases(
            content=related_files_content,
            model=model,
            custom_requirements="Đặc biệt chú trọng test cases cho tích hợp giữa API và UI"
        )
        
        print("\n=== GENERATED TEST CASES ===")
        print(result[:1000] + "..." if len(result) > 1000 else result)
        
        # Parse and count test cases
        test_cases = ai_service.parse_csv_response(result)
        print(f"\nTotal test cases generated: {len(test_cases)}")
        
        # Show some statistics
        prefixes = {}
        for tc in test_cases:
            tc_id = tc.get('ID', '')
            if tc_id:
                prefix = tc_id.split('_')[0] if '_' in tc_id else 'OTHER'
                prefixes[prefix] = prefixes.get(prefix, 0) + 1
        
        print("Test case distribution by prefix:")
        for prefix, count in prefixes.items():
            print(f"  {prefix}: {count} test cases")
            
    except Exception as e:
        print(f"Error generating test cases: {str(e)}")

if __name__ == "__main__":
    demo_multi_file_generation()
