================================================================================
TEST CASES - AAA
Generated on: 2025-07-31 17:46:38
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F1_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra các thành phần giao diện như bộ lọc, tên nhân viên, phòng ban, vị trí, hình thức, ngày bắt đầu, ngày kết thúc, button chỉnh sửa hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần giao diện và dữ liệu theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F1_002
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập giá trị vào ô input 'Bộ lọc' để lọc theo vị trí.
  4. Nhấn Enter hoặc click vào biểu tượng tìm kiếm.
  5. Kiểm tra danh sách nhân viên thử việc hiển thị chỉ chứa các nhân viên có vị trí trùng khớp với giá trị đã nhập.

Kết quả mong muốn:
  Danh sách nhân viên thử việc hiển thị chính xác theo vị trí đã lọc.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F1_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập giá trị vào ô input 'Bộ lọc' để lọc theo phòng ban.
  4. Nhấn Enter hoặc click vào biểu tượng tìm kiếm.
  5. Kiểm tra danh sách nhân viên thử việc hiển thị chỉ chứa các nhân viên có phòng ban trùng khớp với giá trị đã nhập.

Kết quả mong muốn:
  Danh sách nhân viên thử việc hiển thị chính xác theo phòng ban đã lọc.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F1_004
Mục đích kiểm thử: Kiểm tra hiển thị tên nhân viên thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Tên nhân viên' hiển thị đúng tên của nhân viên thử việc.

Kết quả mong muốn:
  Tên nhân viên thử việc hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F1_005
Mục đích kiểm thử: Kiểm tra hiển thị phòng ban của nhân viên thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Phòng ban' hiển thị đúng phòng ban của nhân viên thử việc.

Kết quả mong muốn:
  Phòng ban của nhân viên thử việc hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F1_006
Mục đích kiểm thử: Kiểm tra hiển thị vị trí của nhân viên thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Vị trí' hiển thị đúng vị trí của nhân viên thử việc.

Kết quả mong muốn:
  Vị trí của nhân viên thử việc hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F1_007
Mục đích kiểm thử: Kiểm tra hiển thị hình thức làm việc của nhân viên thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Hình thức' hiển thị đúng hình thức làm việc của nhân viên thử việc.

Kết quả mong muốn:
  Hình thức làm việc của nhân viên thử việc hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F1_008
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu thử việc của nhân viên thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Ngày bắt đầu' hiển thị đúng ngày bắt đầu thử việc của nhân viên thử việc.

Kết quả mong muốn:
  Ngày bắt đầu thử việc của nhân viên thử việc hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F1_009
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc thử việc của nhân viên thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Ngày kết thúc' hiển thị đúng ngày kết thúc thử việc của nhân viên thử việc.

Kết quả mong muốn:
  Ngày kết thúc thử việc của nhân viên thử việc hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F1_010
Mục đích kiểm thử: Kiểm tra button 'Chỉnh sửa' điều hướng đến màn chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào button 'Chỉnh sửa' của một nhân viên thử việc.
  4. Kiểm tra hệ thống điều hướng đến màn hình 'Chỉnh sửa thông tin thử việc' của nhân viên đó.

Kết quả mong muốn:
  Hệ thống điều hướng chính xác đến màn hình 'Chỉnh sửa thông tin thử việc'.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F1_011
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên đã có bản ghi thử việc.
  4. Kiểm tra hệ thống điều hướng đến màn hình 'Thông tin thử việc' của nhân viên đó.

Kết quả mong muốn:
  Hệ thống điều hướng chính xác đến màn hình 'Thông tin thử việc'.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F1_012
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên chưa có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Kiểm tra hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc'.

Kết quả mong muốn:
  Hiển thị popup xác nhận tạo thông tin thử việc.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F1_013
Mục đích kiểm thử: Kiểm tra click button 'Có' trên popup tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Có' trên popup 'Bạn có muốn tạo Thông tin Thử việc'.
  5. Kiểm tra hệ thống điều hướng đến màn hình 'Tạo Thông tin thử việc'.

Kết quả mong muốn:
  Hệ thống điều hướng chính xác đến màn hình 'Tạo Thông tin thử việc'.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F1_014
Mục đích kiểm thử: Kiểm tra click button 'Hủy' trên popup tạo thông tin thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Hủy' trên popup 'Bạn có muốn tạo Thông tin Thử việc'.
  5. Kiểm tra popup đóng lại và không có điều hướng xảy ra.

Kết quả mong muốn:
  Popup đóng lại và không có điều hướng xảy ra.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F2_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình 'Tạo Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Kiểm tra các thành phần giao diện như thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, button thêm/xóa giai đoạn, chọn người đánh giá, chọn mẫu đánh giá, cài đặt trọng số, hạn đánh giá, hạn phê duyệt, chọn kết quả mặc định, gửi thông báo nhắc nhở, button Lưu/Hủy hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần giao diện theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F2_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn hình 'Tạo Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Kiểm tra các button đều enable trừ button 'Lưu'.

Kết quả mong muốn:
  Các button đều enable trừ button 'Lưu'.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F2_003
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Kiểm tra thông tin nhân sự (tên, email, phòng ban, vị trí, hình thức làm việc) hiển thị chính xác.

Kết quả mong muốn:
  Thông tin nhân sự hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F2_004
Mục đích kiểm thử: Kiểm tra chức năng chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Chọn quản lý trực tiếp'.
  3. Chọn một quản lý từ danh sách.
  4. Kiểm tra quản lý đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Quản lý trực tiếp được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F2_005
Mục đích kiểm thử: Kiểm tra chức năng chọn người theo dõi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Chọn người theo dõi'.
  3. Chọn một hoặc nhiều người theo dõi từ danh sách.
  4. Kiểm tra những người theo dõi đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Người theo dõi được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F2_006
Mục đích kiểm thử: Kiểm tra chức năng nhập ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Ngày bắt đầu thử việc'.
  3. Chọn một ngày từ calendar.
  4. Kiểm tra ngày đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Ngày bắt đầu thử việc được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F2_007
Mục đích kiểm thử: Kiểm tra chức năng nhập ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Ngày kết thúc thử việc'.
  3. Chọn một ngày từ calendar.
  4. Kiểm tra ngày đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Ngày kết thúc thử việc được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F2_008
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc (readonly)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày kết thúc thử việc.
  3. Kiểm tra ô 'Ngày kết thúc' (readonly) hiển thị đúng ngày đã nhập.

Kết quả mong muốn:
  Ô 'Ngày kết thúc' hiển thị đúng ngày đã nhập.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F2_009
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu (readonly)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày bắt đầu thử việc.
  3. Kiểm tra ô 'Ngày bắt đầu' (readonly) hiển thị đúng ngày đã nhập.

Kết quả mong muốn:
  Ô 'Ngày bắt đầu' hiển thị đúng ngày đã nhập.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F2_010
Mục đích kiểm thử: Kiểm tra chức năng thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập tên giai đoạn vào ô input 'Tên giai đoạn'.
  3. Click button 'Thêm giai đoạn' (dấu cộng).
  4. Kiểm tra giai đoạn mới được thêm vào danh sách giai đoạn.

Kết quả mong muốn:
  Giai đoạn mới được thêm vào danh sách.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F2_011
Mục đích kiểm thử: Kiểm tra chức năng xóa giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click button 'Xóa' (biểu tượng thùng rác) của giai đoạn vừa thêm.
  4. Kiểm tra giai đoạn đã bị xóa khỏi danh sách giai đoạn.

Kết quả mong muốn:
  Giai đoạn bị xóa khỏi danh sách.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F2_012
Mục đích kiểm thử: Kiểm tra chức năng chọn người đánh giá cho giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Chọn người đánh giá' của giai đoạn.
  4. Chọn một hoặc nhiều người đánh giá từ danh sách.
  5. Kiểm tra những người đánh giá đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Người đánh giá được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F2_013
Mục đích kiểm thử: Kiểm tra chức năng chọn mẫu đánh giá cho người đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá cho giai đoạn.
  4. Click vào ô input 'Chọn mẫu đánh giá' của người đánh giá.
  5. Chọn một mẫu đánh giá từ danh sách.
  6. Kiểm tra mẫu đánh giá đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Mẫu đánh giá được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F2_014
Mục đích kiểm thử: Kiểm tra button 'Cài đặt trọng số chấm điểm' hiển thị form cài đặt trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  4. Click button 'Cài đặt trọng số chấm điểm'.
  5. Kiểm tra form cài đặt trọng số chấm điểm hiển thị.

Kết quả mong muốn:
  Form cài đặt trọng số chấm điểm hiển thị.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F2_015
Mục đích kiểm thử: Kiểm tra chức năng nhập trọng số cho các tiêu chí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  4. Click button 'Cài đặt trọng số chấm điểm'.
  5. Nhập trọng số vào các ô input trọng số.
  6. Kiểm tra trọng số đã nhập hiển thị chính xác trong các ô input.

Kết quả mong muốn:
  Trọng số được nhập và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F2_016
Mục đích kiểm thử: Kiểm tra chức năng nhập hạn đánh giá giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Hạn đánh giá' của giai đoạn.
  4. Chọn một ngày từ calendar.
  5. Kiểm tra ngày đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Hạn đánh giá được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F2_017
Mục đích kiểm thử: Kiểm tra chức năng nhập hạn phê duyệt giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Hạn phê duyệt' của giai đoạn.
  4. Chọn một ngày từ calendar.
  5. Kiểm tra ngày đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Hạn phê duyệt được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F2_018
Mục đích kiểm thử: Kiểm tra chức năng chọn kết quả mặc định phê duyệt cho giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Chọn kết quả mặc định' của giai đoạn.
  4. Chọn một kết quả (Pass/Fail) từ danh sách.
  5. Kiểm tra kết quả đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Kết quả mặc định được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F2_019
Mục đích kiểm thử: Kiểm tra chức năng chọn số ngày gửi thông báo nhắc nhở đánh giá trước
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Gửi thông báo nhắc nhở đánh giá trước'.
  3. Chọn một số ngày từ danh sách.
  4. Kiểm tra số ngày đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Số ngày được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F2_020
Mục đích kiểm thử: Kiểm tra button 'Lưu' lưu thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập đầy đủ thông tin thử việc.
  3. Click button 'Lưu'.
  4. Kiểm tra thông tin thử việc được lưu thành công và hiển thị thông báo thành công.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F2_021
Mục đích kiểm thử: Kiểm tra button 'Hủy' hủy tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập thông tin thử việc.
  3. Click button 'Hủy'.
  4. Kiểm tra hệ thống quay trở lại màn hình danh sách thử việc và không có thông tin nào được lưu.

Kết quả mong muốn:
  Hệ thống quay trở lại màn hình danh sách thử việc và không có thông tin nào được lưu.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F3_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình 'Chỉnh sửa Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.
  2. Kiểm tra các thành phần giao diện như thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, button thêm/xóa giai đoạn, chọn người đánh giá, chọn mẫu đánh giá, cài đặt trọng số, hạn đánh giá, hạn phê duyệt, chọn kết quả mặc định, gửi thông báo nhắc nhở, button Lưu/Hủy hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần giao diện theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F3_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn hình 'Chỉnh sửa Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.
  2. Kiểm tra các button đều enable, button 'Lưu cập nhật' disable, các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

Kết quả mong muốn:
  Các button đều enable, button 'Lưu cập nhật' disable, các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F3_003
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.
  2. Kiểm tra thông tin nhân sự (tên, email, phòng ban, vị trí, hình thức làm việc) hiển thị chính xác.

Kết quả mong muốn:
  Thông tin nhân sự hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F3_004
Mục đích kiểm thử: Kiểm tra chức năng chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.
  2. Click vào ô input 'Chọn quản lý trực tiếp'.
  3. Chọn một quản lý từ danh sách.
  4. Kiểm tra quản lý đã chọn hiển thị trong ô input.

Kết quả mong muốn:
  Quản lý trực tiếp được chọn và hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F3_005
Mục đích kiểm thử: Kiểm tra chức năng chọn người theo
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 40
Generated by Test Case Generator v1.0
