================================================================================
TEST CASES - AAA
Generated on: 2025-07-31 17:53:21
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F1_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra các thành phần giao diện: Bộ lọc, Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần giao diện theo mô tả.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F1_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra trạng thái của các button.

Kết quả mong muốn:
  Các button đều enable trừ button 'Lưu'/'Lưu cập nhật' disable.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F1_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo tên nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập tên nhân viên vào ô input 'Bộ lọc'.
  4. Nhấn Enter hoặc click vào biểu tượng tìm kiếm.

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên có tên trùng khớp với điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F1_004
Mục đích kiểm thử: Kiểm tra hiển thị tên nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Tên nhân viên được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F1_005
Mục đích kiểm thử: Kiểm tra hiển thị phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Phòng ban của nhân viên được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F1_006
Mục đích kiểm thử: Kiểm tra hiển thị vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Vị trí của nhân viên được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F1_007
Mục đích kiểm thử: Kiểm tra hiển thị hình thức
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Hình thức làm việc của nhân viên được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F1_008
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu thử việc của nhân viên được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F1_009
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Ngày kết thúc thử việc của nhân viên được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F1_010
Mục đích kiểm thử: Kiểm tra button 'Chỉnh sửa'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào button 'Chỉnh sửa' của một nhân viên.

Kết quả mong muốn:
  Điều hướng đến màn hình chỉnh sửa thông tin thử việc của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F1_011
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên đã có bản ghi thử việc.

Kết quả mong muốn:
  Chuyển đến màn hình 'Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F1_012
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên chưa có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc'.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F1_013
Mục đích kiểm thử: Kiểm tra popup tạo thông tin thử việc - Click 'Có'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Có' trên popup.

Kết quả mong muốn:
  Chuyển đến màn hình tạo 'Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F1_014
Mục đích kiểm thử: Kiểm tra popup tạo thông tin thử việc - Click 'Hủy'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Hủy' trên popup.

Kết quả mong muốn:
  Popup đóng lại.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO/CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F2_001
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn hình tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.

Kết quả mong muốn:
  Các button đều enable.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F2_002
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm: tên nhân sự, email, phòng ban, vị trí, hình thức làm việc.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F2_003
Mục đích kiểm thử: Kiểm tra chức năng chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click vào ô input 'Chọn quản lý trực tiếp'.
  3. Chọn một quản lý từ danh sách.

Kết quả mong muốn:
  Quản lý được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F2_004
Mục đích kiểm thử: Kiểm tra chức năng chọn người theo dõi
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click vào ô input 'Chọn người theo dõi'.
  3. Chọn một hoặc nhiều người theo dõi từ danh sách.

Kết quả mong muốn:
  Người theo dõi được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F2_005
Mục đích kiểm thử: Kiểm tra nhập ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu thử việc được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F2_006
Mục đích kiểm thử: Kiểm tra nhập ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc thử việc được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F2_007
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc (tự động)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F2_008
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu (tự động)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F2_009
Mục đích kiểm thử: Kiểm tra chức năng 'Thêm giai đoạn'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập tên giai đoạn vào ô input.
  3. Click button 'Thêm giai đoạn'.

Kết quả mong muốn:
  Giai đoạn mới được thêm vào danh sách giai đoạn.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F2_010
Mục đích kiểm thử: Kiểm tra chức năng 'Xóa giai đoạn'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click button 'Xóa giai đoạn' của một giai đoạn.

Kết quả mong muốn:
  Giai đoạn đó bị xóa khỏi danh sách giai đoạn.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F2_011
Mục đích kiểm thử: Kiểm tra chức năng 'Chọn người đánh giá'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click vào ô input 'Chọn người đánh giá' trong một giai đoạn.
  3. Chọn một hoặc nhiều người đánh giá từ danh sách.

Kết quả mong muốn:
  Người đánh giá được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F2_012
Mục đích kiểm thử: Kiểm tra chức năng 'Chọn mẫu đánh giá'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click vào ô input 'Chọn mẫu đánh giá' cho một người đánh giá.
  3. Chọn một mẫu đánh giá từ danh sách.

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F2_013
Mục đích kiểm thử: Kiểm tra button 'Cài đặt trọng số chấm điểm'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click button 'Cài đặt trọng số chấm điểm'.

Kết quả mong muốn:
  Hiển thị form cài đặt trọng số chấm điểm.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F2_014
Mục đích kiểm thử: Kiểm tra input nhập trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click button 'Cài đặt trọng số chấm điểm'.
  3. Nhập trọng số vào ô input 'Input nhập trọng số'.

Kết quả mong muốn:
  Trọng số được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F2_015
Mục đích kiểm thử: Kiểm tra nhập hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập hạn đánh giá vào ô input 'Hạn đánh giá'.

Kết quả mong muốn:
  Hạn đánh giá được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F2_016
Mục đích kiểm thử: Kiểm tra nhập hạn phê duyệt
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập hạn phê duyệt vào ô input 'Hạn phê duyệt'.

Kết quả mong muốn:
  Hạn phê duyệt được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F2_017
Mục đích kiểm thử: Kiểm tra chức năng 'Chọn kết quả mặc định'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Chọn một kết quả mặc định từ dropdown 'Chọn kết quả mặc định'.

Kết quả mong muốn:
  Kết quả mặc định được chọn hiển thị trong dropdown.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F2_018
Mục đích kiểm thử: Kiểm tra chức năng 'Gửi thông báo nhắc nhở đánh giá trước'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Chọn số ngày từ input 'Gửi thông báo nhắc nhở đánh giá trước'.

Kết quả mong muốn:
  Số ngày được chọn hiển thị trong input.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F2_019
Mục đích kiểm thử: Kiểm tra button 'Lưu'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập đầy đủ thông tin hợp lệ.
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và quay trở lại màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F2_020
Mục đích kiểm thử: Kiểm tra button 'Hủy'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Click button 'Hủy'.

Kết quả mong muốn:
  Hủy tạo thử việc và quay trở lại màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F2_021
Mục đích kiểm thử: Kiểm tra validation bắt buộc các trường
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Để trống các trường bắt buộc (direct_manager_id, start_date, end_date, evaluator_id, evaluation_template_id, weight_factor).
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi cho các trường bắt buộc bị bỏ trống.

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: F2_022
Mục đích kiểm thử: Kiểm tra validation kiểu dữ liệu cho trọng số
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo thử việc'.
  2. Nhập giá trị không phải số nguyên dương vào trường 'Input nhập trọng số'.
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập số nguyên dương.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F3_001
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn hình chỉnh sửa thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.

Kết quả mong muốn:
  Các button đều enable, btn 'Lưu cập nhật' disable, các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F3_002
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm: tên nhân sự, email, phòng ban, vị trí, hình thức làm việc.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F3_003
Mục đích kiểm thử: Kiểm tra chức năng chọn quản lý trực tiếp trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click vào ô input 'Chọn quản lý trực tiếp'.
  3. Chọn một quản lý từ danh sách.

Kết quả mong muốn:
  Quản lý được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F3_004
Mục đích kiểm thử: Kiểm tra chức năng chọn người theo dõi trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click vào ô input 'Chọn người theo dõi'.
  3. Chọn một hoặc nhiều người theo dõi từ danh sách.

Kết quả mong muốn:
  Người theo dõi được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F3_005
Mục đích kiểm thử: Kiểm tra nhập ngày bắt đầu thử việc trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu thử việc được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F3_006
Mục đích kiểm thử: Kiểm tra nhập ngày kết thúc thử việc trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc thử việc được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F3_007
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc (tự động) trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F3_008
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu (tự động) trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu được hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F3_009
Mục đích kiểm thử: Kiểm tra chức năng 'Thêm giai đoạn' trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập tên giai đoạn vào ô input.
  3. Click button 'Thêm giai đoạn'.

Kết quả mong muốn:
  Giai đoạn mới được thêm vào danh sách giai đoạn.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F3_010
Mục đích kiểm thử: Kiểm tra chức năng 'Xóa giai đoạn' trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click button 'Xóa giai đoạn' của một giai đoạn chưa có kết quả đánh giá/phê duyệt.

Kết quả mong muốn:
  Giai đoạn đó bị xóa khỏi danh sách giai đoạn.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F3_011
Mục đích kiểm thử: Kiểm tra chức năng 'Chọn người đánh giá' trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click vào ô input 'Chọn người đánh giá' trong một giai đoạn.
  3. Chọn một hoặc nhiều người đánh giá từ danh sách.

Kết quả mong muốn:
  Người đánh giá được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F3_012
Mục đích kiểm thử: Kiểm tra chức năng 'Chọn mẫu đánh giá' trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click vào ô input 'Chọn mẫu đánh giá' cho một người đánh giá.
  3. Chọn một mẫu đánh giá từ danh sách.

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F3_013
Mục đích kiểm thử: Kiểm tra button 'Cài đặt trọng số chấm điểm' trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click button 'Cài đặt trọng số chấm điểm'.

Kết quả mong muốn:
  Hiển thị form cài đặt trọng số chấm điểm.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F3_014
Mục đích kiểm thử: Kiểm tra input nhập trọng số trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click button 'Cài đặt trọng số chấm điểm'.
  3. Nhập trọng số vào ô input 'Input nhập trọng số'.

Kết quả mong muốn:
  Trọng số được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F3_015
Mục đích kiểm thử: Kiểm tra nhập hạn đánh giá trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập hạn đánh giá vào ô input 'Hạn đánh giá'.

Kết quả mong muốn:
  Hạn đánh giá được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F3_016
Mục đích kiểm thử: Kiểm tra nhập hạn phê duyệt trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập hạn phê duyệt vào ô input 'Hạn phê duyệt'.

Kết quả mong muốn:
  Hạn phê duyệt được nhập chính xác.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F3_017
Mục đích kiểm thử: Kiểm tra chức năng 'Chọn kết quả mặc định' trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Chọn một kết quả mặc định từ dropdown 'Chọn kết quả mặc định'.

Kết quả mong muốn:
  Kết quả mặc định được chọn hiển thị trong dropdown.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F3_018
Mục đích kiểm thử: Kiểm tra chức năng 'Gửi thông báo nhắc nhở đánh giá trước' trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Chọn số ngày từ input 'Gửi thông báo nhắc nhở đánh giá trước'.

Kết quả mong muốn:
  Số ngày được chọn hiển thị trong input.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F3_019
Mục đích kiểm thử: Kiểm tra button 'Lưu' trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Thay đổi thông tin hợp lệ.
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và quay trở lại màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F3_020
Mục đích kiểm thử: Kiểm tra button 'Hủy' trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Click button 'Hủy'.

Kết quả mong muốn:
  Hủy chỉnh sửa thử việc và quay trở lại màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F3_021
Mục đích kiểm thử: Kiểm tra validation bắt buộc các trường trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Để trống các trường bắt buộc (direct_manager_id, start_date, end_date, evaluator_id, evaluation_template_id, weight_factor).
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi cho các trường bắt buộc bị bỏ trống.

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: F3_022
Mục đích kiểm thử: Kiểm tra validation kiểu dữ liệu cho trọng số trên màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa thử việc'.
  2. Nhập giá trị không phải số nguyên dương vào trường 'Input nhập trọng số'.
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập số nguyên dương.

--------------------------------------------------------------------------------

Test Case #23
----------------------------------------
ID: INT_001
Mục đích kiểm thử: Kiểm tra luồng tạo mới thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Danh sách thử việc'.
  2. Click vào tên nhân viên chưa có thông tin thử việc.
  3. Click 'Có' để tạo thông tin thử việc.
  4. Nhập đầy đủ thông tin hợp lệ trên màn hình 'Tạo thử việc'.
  5. Click 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được tạo thành công và hiển thị trên màn hình 'Danh sách thử việc'.

--------------------------------------------------------------------------------

Test Case #24
----------------------------------------
ID: INT_002
Mục đích kiểm thử: Kiểm tra luồng chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Danh sách thử việc'.
  2. Click vào button 'Chỉnh sửa' của một nhân viên.
  3. Thay đổi thông tin hợp lệ trên màn hình 'Chỉnh sửa thử việc'.
  4. Click 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được cập nhật thành công và hiển thị trên màn hình 'Danh sách thử việc'.

--------------------------------------------------------------------------------

Test Case #25
----------------------------------------
ID: E2E_001
Mục đích kiểm thử: Kiểm tra luồng tạo và chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Thực hiện các bước của INT_001 để tạo thông tin thử việc.
  2. Thực hiện các bước của INT_002 để chỉnh sửa thông tin thử việc.

Kết quả mong muốn:
  Thông tin thử việc được tạo và chỉnh sửa thành công.

--------------------------------------------------------------------------------

Test Case #26
----------------------------------------
ID: SYS_001
Mục đích kiểm thử: Kiểm tra quyền chỉnh sửa thông tin thử việc của quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập với tài khoản quản lý trực tiếp.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào button 'Chỉnh sửa' của nhân viên dưới quyền.

Kết quả mong muốn:
  Quản lý trực tiếp có quyền chỉnh sửa thông tin thử việc của nhân viên dưới quyền.

--------------------------------------------------------------------------------

Test Case #27
----------------------------------------
ID: SYS_002
Mục đích kiểm thử: Kiểm tra quyền xem thông tin thử việc của người theo dõi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập với tài khoản người theo dõi.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên được theo dõi.

Kết quả mong muốn:
  Người theo dõi có quyền xem thông tin thử việc của nhân viên được theo dõi.

--------------------------------------------------------------------------------

Test Case #28
----------------------------------------
ID: SYS_003
Mục đích kiểm thử: Kiểm tra thông báo khi có người comment vào thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập với tài khoản người theo dõi.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên được theo dõi.
  4. Thêm comment vào thông tin thử việc.
  5. Kiểm tra thông báo.

Kết quả mong muốn:
  Người theo dõi nhận được thông báo khi có người comment vào thử việc.

--------------------------------------------------------------------------------

Test Case #29
----------------------------------------
ID: SYS_004
Mục đích kiểm thử: Kiểm tra hệ thống tự động phê duyệt giai đoạn khi hết hạn phê duyệt
Độ ưu tiên: High

Các bước thực hiện:
  1. Tạo thông tin thử việc với hạn phê duyệt trong quá khứ.
  2. Chọn kết quả mặc định là 'Pass' hoặc 'Fail'.
  3. Chờ hết hạn phê duyệt.
  4. Kiểm tra kết quả phê duyệt giai đoạn.

Kết quả mong muốn:
  Hệ thống tự động phê duyệt giai đoạn theo kết quả mặc định đã chọn.

--------------------------------------------------------------------------------

Test Case #30
----------------------------------------
ID: SYS_005
Mục đích kiểm thử: Kiểm tra gửi thông báo nhắc nhở đánh giá trước hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Tạo thông tin thử việc với hạn đánh giá trong tương lai.
  2. Chọn số ngày gửi thông báo nhắc nhở đánh giá trước.
  3. Chờ đến ngày gửi thông báo.
  4. Kiểm tra thông báo.

Kết quả mong muốn:
  Người đánh giá nhận được thông báo nhắc nhở đánh giá trước hạn đánh giá.

--------------------------------------------------------------------------------


Total Test Cases Generated: 66
Generated by Test Case Generator v1.0
