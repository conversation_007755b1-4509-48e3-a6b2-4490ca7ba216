================================================================================
TEST CASES - AAA
Generated on: 2025-07-31 17:40:35
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F1_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra các thành phần giao diện như bộ lọc, tên nhân viên, phòng ban, vị trí, hình thức, ngày bắt đầu, ngày kết thúc, button Chỉnh sửa hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần giao diện và dữ liệu theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F1_002
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập giá trị vào ô input 'Bộ lọc' để lọc theo vị trí.
  4. Nhấn Enter hoặc click vào biểu tượng tìm kiếm.

Kết quả mong muốn:
  Danh sách nhân viên thử việc được lọc theo vị trí đã nhập.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F1_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập giá trị vào ô input 'Bộ lọc' để lọc theo phòng ban.
  4. Nhấn Enter hoặc click vào biểu tượng tìm kiếm.

Kết quả mong muốn:
  Danh sách nhân viên thử việc được lọc theo phòng ban đã nhập.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F1_004
Mục đích kiểm thử: Kiểm tra hiển thị tên nhân viên thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Tên nhân viên'.

Kết quả mong muốn:
  Cột 'Tên nhân viên' hiển thị tên đầy đủ của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F1_005
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên khi đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên một nhân viên đã có bản ghi thử việc.
  4. Kiểm tra chuyển hướng.

Kết quả mong muốn:
  Hệ thống chuyển hướng đến màn hình 'Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F1_006
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên khi chưa có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên một nhân viên chưa có bản ghi thử việc.
  4. Kiểm tra hiển thị popup.
  5. Click button 'Có' trên popup.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc'. Sau khi click 'Có', hệ thống chuyển hướng đến màn hình 'Tạo Thông tin thử việc'.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F1_007
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên khi chưa có bản ghi thử việc - Hủy tạo
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên một nhân viên chưa có bản ghi thử việc.
  4. Kiểm tra hiển thị popup.
  5. Click button 'Hủy' trên popup.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc'. Sau khi click 'Hủy', popup đóng lại và không có chuyển hướng.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F1_008
Mục đích kiểm thử: Kiểm tra hiển thị phòng ban của nhân viên thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Phòng ban'.

Kết quả mong muốn:
  Cột 'Phòng ban' hiển thị phòng ban chính xác của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F1_009
Mục đích kiểm thử: Kiểm tra hiển thị vị trí của nhân viên thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Vị trí'.

Kết quả mong muốn:
  Cột 'Vị trí' hiển thị vị trí làm việc chính xác của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F1_010
Mục đích kiểm thử: Kiểm tra hiển thị hình thức làm việc của nhân viên thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Hình thức'.

Kết quả mong muốn:
  Cột 'Hình thức' hiển thị hình thức làm việc chính xác của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F1_011
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu thử việc của nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Ngày bắt đầu'.

Kết quả mong muốn:
  Cột 'Ngày bắt đầu' hiển thị ngày bắt đầu thử việc chính xác của nhân viên.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F1_012
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc thử việc của nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Ngày kết thúc'.

Kết quả mong muốn:
  Cột 'Ngày kết thúc' hiển thị ngày kết thúc thử việc chính xác của nhân viên.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F1_013
Mục đích kiểm thử: Kiểm tra button 'Chỉnh sửa'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào button 'Chỉnh sửa' của một nhân viên.
  4. Kiểm tra chuyển hướng.

Kết quả mong muốn:
  Hệ thống chuyển hướng đến màn hình 'Chỉnh sửa thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F2_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc' (ví dụ: từ màn hình danh sách thử việc khi click vào tên nhân viên chưa có thông tin thử việc).
  3. Kiểm tra các thành phần giao diện như thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, các button Thêm/Xóa giai đoạn, chọn người đánh giá, chọn mẫu đánh giá, cài đặt trọng số, hạn đánh giá, hạn phê duyệt, chọn kết quả mặc định, gửi thông báo nhắc nhở, button Lưu/Hủy hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần giao diện theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F2_002
Mục đích kiểm thử: Kiểm tra chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Click vào ô input 'Chọn quản lý trực tiếp'.
  4. Chọn một quản lý từ danh sách hiển thị.

Kết quả mong muốn:
  Quản lý trực tiếp được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F2_003
Mục đích kiểm thử: Kiểm tra chọn nhiều người theo dõi
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Click vào ô input 'Chọn người theo dõi'.
  4. Chọn nhiều người theo dõi từ danh sách hiển thị.

Kết quả mong muốn:
  Nhiều người theo dõi được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F2_004
Mục đích kiểm thử: Kiểm tra nhập ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu thử việc được nhập hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F2_005
Mục đích kiểm thử: Kiểm tra nhập ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc thử việc được nhập hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F2_006
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc (readonly)
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.
  4. Kiểm tra ô 'Ngày kết thúc' (readonly).

Kết quả mong muốn:
  Ô 'Ngày kết thúc' (readonly) hiển thị ngày kết thúc thử việc đã nhập.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F2_007
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu (readonly)
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.
  4. Kiểm tra ô 'Ngày bắt đầu' (readonly).

Kết quả mong muốn:
  Ô 'Ngày bắt đầu' (readonly) hiển thị ngày bắt đầu thử việc đã nhập.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F2_008
Mục đích kiểm thử: Kiểm tra thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Click vào button 'Thêm giai đoạn'.
  4. Nhập tên giai đoạn vào ô input mới hiển thị.
  5. Click button 'Thêm' (dấu cộng).

Kết quả mong muốn:
  Một giai đoạn thử việc mới được thêm vào danh sách.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F2_009
Mục đích kiểm thử: Kiểm tra xóa giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Click vào button 'Xóa' (biểu tượng thùng rác) của giai đoạn đó.

Kết quả mong muốn:
  Giai đoạn thử việc được xóa khỏi danh sách.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F2_010
Mục đích kiểm thử: Kiểm tra chọn người đánh giá cho giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Click vào ô input 'Chọn người đánh giá' của giai đoạn đó.
  5. Chọn một hoặc nhiều người đánh giá từ danh sách hiển thị.

Kết quả mong muốn:
  Người đánh giá được chọn hiển thị trong ô input của giai đoạn đó.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F2_011
Mục đích kiểm thử: Kiểm tra chọn mẫu đánh giá cho người đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Chọn một người đánh giá cho giai đoạn đó.
  5. Click vào ô input 'Chọn mẫu đánh giá' tương ứng với người đánh giá.
  6. Chọn một mẫu đánh giá từ danh sách hiển thị.

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô input tương ứng với người đánh giá.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F2_012
Mục đích kiểm thử: Kiểm tra hiển thị form cài đặt trọng số chấm điểm
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn đó.
  5. Click vào button 'Cài đặt trọng số chấm điểm'.

Kết quả mong muốn:
  Form 'Cài đặt trọng số chấm điểm' hiển thị.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F2_013
Mục đích kiểm thử: Kiểm tra nhập trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn đó.
  5. Click vào button 'Cài đặt trọng số chấm điểm'.
  6. Nhập trọng số vào các ô input tương ứng với các tiêu chí.

Kết quả mong muốn:
  Trọng số được nhập hiển thị chính xác trong các ô input.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F2_014
Mục đích kiểm thử: Kiểm tra nhập hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Nhập hạn đánh giá vào ô input 'Hạn đánh giá' của giai đoạn đó.

Kết quả mong muốn:
  Hạn đánh giá được nhập hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F2_015
Mục đích kiểm thử: Kiểm tra nhập hạn phê duyệt
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Nhập hạn phê duyệt vào ô input 'Hạn phê duyệt' của giai đoạn đó.

Kết quả mong muốn:
  Hạn phê duyệt được nhập hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F2_016
Mục đích kiểm thử: Kiểm tra chọn kết quả mặc định
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc (nếu chưa có).
  4. Chọn một kết quả mặc định (Pass/Fail) từ dropdown 'Chọn kết quả mặc định' của giai đoạn đó.

Kết quả mong muốn:
  Kết quả mặc định được chọn hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F2_017
Mục đích kiểm thử: Kiểm tra chọn số ngày gửi thông báo nhắc nhở
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Chọn số ngày từ input 'Gửi thông báo nhắc nhở đánh giá trước'.

Kết quả mong muốn:
  Số ngày được chọn hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F2_018
Mục đích kiểm thử: Kiểm tra button 'Lưu'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Nhập đầy đủ thông tin hợp lệ vào các trường bắt buộc.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và hệ thống chuyển hướng đến màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F2_019
Mục đích kiểm thử: Kiểm tra button 'Hủy'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Click button 'Hủy'.

Kết quả mong muốn:
  Hệ thống hủy tạo thông tin thử việc và chuyển hướng đến màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F2_020
Mục đích kiểm thử: Kiểm tra validation trường 'Chọn quản lý trực tiếp'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Để trống trường 'Chọn quản lý trực tiếp'.
  4. Nhập các thông tin khác hợp lệ.
  5. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn quản lý trực tiếp'.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F2_021
Mục đích kiểm thử: Kiểm tra validation trường 'Ngày bắt đầu thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Để trống trường 'Ngày bắt đầu thử việc'.
  4. Nhập các thông tin khác hợp lệ.
  5. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập ngày bắt đầu thử việc'.

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: F2_022
Mục đích kiểm thử: Kiểm tra validation trường 'Ngày kết thúc thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Để trống trường 'Ngày kết thúc thử việc'.
  4. Nhập các thông tin khác hợp lệ.
  5. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập ngày kết thúc thử việc'.

--------------------------------------------------------------------------------

Test Case #23
----------------------------------------
ID: F2_023
Mục đích kiểm thử: Kiểm tra validation trường 'Người đánh giá' trong giai đoạn
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc.
  4. Để trống trường 'Chọn người đánh giá' trong giai đoạn.
  5. Nhập các thông tin khác hợp lệ.
  6. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn người đánh giá cho giai đoạn'.

--------------------------------------------------------------------------------

Test Case #24
----------------------------------------
ID: F2_024
Mục đích kiểm thử: Kiểm tra validation trường 'Mẫu đánh giá' trong giai đoạn
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc.
  4. Chọn một người đánh giá cho giai đoạn.
  5. Để trống trường 'Chọn mẫu đánh giá' trong giai đoạn.
  6. Nhập các thông tin khác hợp lệ.
  7. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn mẫu đánh giá cho người đánh giá'.

--------------------------------------------------------------------------------

Test Case #25
----------------------------------------
ID: F2_025
Mục đích kiểm thử: Kiểm tra validation trường 'Trọng số' trong giai đoạn
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc.
  4. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  5. Click button 'Cài đặt trọng số chấm điểm'.
  6. Để trống một trong các trường 'Trọng số'.
  7. Nhập các thông tin khác hợp lệ.
  8. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập trọng số cho tất cả các tiêu chí'.

--------------------------------------------------------------------------------

Test Case #26
----------------------------------------
ID: F2_026
Mục đích kiểm thử: Kiểm tra validation trường 'Trọng số' nhập giá trị không hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Tạo thông tin thử việc'.
  3. Thêm một giai đoạn thử việc.
  4. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  5. Click button 'Cài đặt trọng số chấm điểm'.
  6. Nhập giá trị không phải số nguyên dương vào một trong các trường 'Trọng số'.
  7. Nhập các thông tin khác hợp lệ.
  8. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Trọng số phải là số nguyên dương'.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH CHỈNH SỬA THỬ VIỆC
============================================================


Total Test Cases Generated: 39
Generated by Test Case Generator v1.0
