================================================================================
TEST CASES - AAA
Generated on: 2025-07-31 17:50:42
================================================================================


============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_005
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_006
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE TÌM KIẾM
============================================================



============================================================
KIỂM TRA CHỨC NĂNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_007
Mục đích kiểm thử: Kiểm tra tìm kiếm với từ khóa chính xác
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc nút Tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách kết quả khớp với từ khóa

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_008
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_009
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_010
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_011
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_012
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_013
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_014
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_015
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_016
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_017
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_018
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_019
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_020
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_021
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_022
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_023
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_024
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_025
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_026
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_027
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_028
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_029
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_030
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_031
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_032
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_033
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_034
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_035
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_036
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_037
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_038
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_039
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_040
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_041
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_042
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_043
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_044
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_045
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_046
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_047
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_048
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_049
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_050
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================


Total Test Cases Generated: 50
Generated by Test Case Generator v1.0
