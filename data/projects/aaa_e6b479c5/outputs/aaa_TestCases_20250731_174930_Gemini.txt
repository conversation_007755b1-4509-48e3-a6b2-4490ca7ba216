================================================================================
TEST CASES - AAA
Generated on: 2025-07-31 17:49:30
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F1_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra các thành phần giao diện như bộ lọc, tên nhân viên, phòng ban, vị trí, hình thức, ngày bắt đầu, ngày kết thúc, button Chỉnh sửa hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần và dữ liệu theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F1_002
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập vị trí vào ô input 'Bộ lọc'.
  4. Nhấn Enter hoặc click vào biểu tượng tìm kiếm.
  5. Kiểm tra danh sách nhân viên hiển thị.

Kết quả mong muốn:
  Danh sách hiển thị chỉ những nhân viên có vị trí trùng khớp với điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F1_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập phòng ban vào ô input 'Bộ lọc'.
  4. Nhấn Enter hoặc click vào biểu tượng tìm kiếm.
  5. Kiểm tra danh sách nhân viên hiển thị.

Kết quả mong muốn:
  Danh sách hiển thị chỉ những nhân viên có phòng ban trùng khớp với điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F1_004
Mục đích kiểm thử: Kiểm tra hiển thị tên nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Tên nhân viên'.

Kết quả mong muốn:
  Cột 'Tên nhân viên' hiển thị tên đầy đủ của nhân viên.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F1_005
Mục đích kiểm thử: Kiểm tra hiển thị phòng ban
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Phòng ban'.

Kết quả mong muốn:
  Cột 'Phòng ban' hiển thị phòng ban của nhân viên.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F1_006
Mục đích kiểm thử: Kiểm tra hiển thị vị trí
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Vị trí'.

Kết quả mong muốn:
  Cột 'Vị trí' hiển thị vị trí của nhân viên.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F1_007
Mục đích kiểm thử: Kiểm tra hiển thị hình thức
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Hình thức'.

Kết quả mong muốn:
  Cột 'Hình thức' hiển thị hình thức làm việc của nhân viên.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F1_008
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Ngày bắt đầu'.

Kết quả mong muốn:
  Cột 'Ngày bắt đầu' hiển thị ngày bắt đầu thử việc của nhân viên.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F1_009
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Kiểm tra cột 'Ngày kết thúc'.

Kết quả mong muốn:
  Cột 'Ngày kết thúc' hiển thị ngày kết thúc thử việc của nhân viên.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F1_010
Mục đích kiểm thử: Kiểm tra button 'Chỉnh sửa'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào button 'Chỉnh sửa' của một nhân viên.
  4. Kiểm tra chuyển hướng.

Kết quả mong muốn:
  Hệ thống chuyển hướng đến màn hình 'Chỉnh sửa thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F1_011
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên đã có bản ghi thử việc.
  4. Kiểm tra chuyển hướng.

Kết quả mong muốn:
  Hệ thống chuyển hướng đến màn hình 'Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F1_012
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên chưa có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Kiểm tra popup hiển thị.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc'.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F1_013
Mục đích kiểm thử: Kiểm tra popup tạo thông tin thử việc - Button 'Có'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Có' trên popup.
  5. Kiểm tra chuyển hướng.

Kết quả mong muốn:
  Hệ thống chuyển hướng đến màn hình 'Tạo Thông tin thử việc'.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F1_014
Mục đích kiểm thử: Kiểm tra popup tạo thông tin thử việc - Button 'Hủy'
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Hủy' trên popup.
  5. Kiểm tra popup đóng.

Kết quả mong muốn:
  Popup đóng và không có chuyển hướng.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F2_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình 'Tạo Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Kiểm tra các thành phần giao diện như thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, button Thêm giai đoạn, button Xóa giai đoạn, chọn người đánh giá, chọn mẫu đánh giá, cài đặt trọng số, hạn đánh giá, hạn phê duyệt, chọn kết quả mặc định, gửi thông báo nhắc nhở, button Lưu, button Hủy hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần và dữ liệu theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F2_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Kiểm tra trạng thái enable/disable của các button 'Lưu' và 'Hủy'.

Kết quả mong muốn:
  Các button đều enable trừ button 'Lưu' disable.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F2_003
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Kiểm tra thông tin nhân sự hiển thị.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm: tên nhân sự, email, phòng ban, vị trí, hình thức làm việc.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F2_004
Mục đích kiểm thử: Kiểm tra chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Chọn quản lý trực tiếp'.
  3. Chọn một quản lý từ danh sách.

Kết quả mong muốn:
  Quản lý được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F2_005
Mục đích kiểm thử: Kiểm tra chọn người theo dõi
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Chọn người theo dõi'.
  3. Chọn một hoặc nhiều người theo dõi từ danh sách.

Kết quả mong muốn:
  Người theo dõi được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F2_006
Mục đích kiểm thử: Kiểm tra nhập ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu thử việc được nhập hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F2_007
Mục đích kiểm thử: Kiểm tra nhập ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc thử việc được nhập hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F2_008
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày kết thúc thử việc vào ô input 'Ngày kết thúc thử việc'.
  3. Kiểm tra hiển thị ngày kết thúc.

Kết quả mong muốn:
  Hiển thị ngày kết thúc đã được nhập ở mục 5.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F2_009
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày bắt đầu thử việc vào ô input 'Ngày bắt đầu thử việc'.
  3. Kiểm tra hiển thị ngày bắt đầu.

Kết quả mong muốn:
  Hiển thị ngày bắt đầu đã được nhập ở mục 4.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F2_010
Mục đích kiểm thử: Kiểm tra button 'Thêm giai đoạn'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập tên giai đoạn vào ô input.
  3. Click button 'Thêm giai đoạn'.

Kết quả mong muốn:
  Giai đoạn mới được thêm hiển thị ở phía dưới.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F2_011
Mục đích kiểm thử: Kiểm tra button 'Xóa giai đoạn'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click button 'Xóa giai đoạn' của giai đoạn vừa thêm.

Kết quả mong muốn:
  Giai đoạn vừa thêm bị xóa.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F2_012
Mục đích kiểm thử: Kiểm tra chọn người đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Chọn người đánh giá' trong giai đoạn vừa thêm.
  4. Chọn một hoặc nhiều người đánh giá từ danh sách.

Kết quả mong muốn:
  Người đánh giá được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F2_013
Mục đích kiểm thử: Kiểm tra chọn mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Chọn mẫu đánh giá' trong giai đoạn vừa thêm.
  4. Chọn một mẫu đánh giá từ danh sách.

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F2_014
Mục đích kiểm thử: Kiểm tra button 'Cài đặt trọng số chấm điểm'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click button 'Cài đặt trọng số chấm điểm' trong giai đoạn vừa thêm.

Kết quả mong muốn:
  Hiển thị form 'Cài đặt trọng số chấm điểm'.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F2_015
Mục đích kiểm thử: Kiểm tra input nhập trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click button 'Cài đặt trọng số chấm điểm' trong giai đoạn vừa thêm.
  4. Nhập trọng số vào ô input 'Input nhập trọng số'.

Kết quả mong muốn:
  Trọng số được nhập hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F2_016
Mục đích kiểm thử: Kiểm tra nhập hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Nhập hạn đánh giá vào ô input 'Hạn đánh giá'.

Kết quả mong muốn:
  Hạn đánh giá được nhập hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F2_017
Mục đích kiểm thử: Kiểm tra nhập hạn phê duyệt
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Nhập hạn phê duyệt vào ô input 'Hạn phê duyệt'.

Kết quả mong muốn:
  Hạn phê duyệt được nhập hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F2_018
Mục đích kiểm thử: Kiểm tra chọn kết quả mặc định
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Chọn kết quả mặc định'.
  4. Chọn một kết quả mặc định từ danh sách.

Kết quả mong muốn:
  Kết quả mặc định được chọn hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F2_019
Mục đích kiểm thử: Kiểm tra gửi thông báo nhắc nhở đánh giá trước
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập số ngày vào input 'Gửi thông báo nhắc nhở đánh giá trước'.

Kết quả mong muốn:
  Số ngày được nhập hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F2_020
Mục đích kiểm thử: Kiểm tra button 'Lưu'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập đầy đủ thông tin hợp lệ.
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và chuyển về màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F2_021
Mục đích kiểm thử: Kiểm tra button 'Hủy'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập thông tin.
  3. Click button 'Hủy'.

Kết quả mong muốn:
  Hủy tạo mới thử việc và quay trở lại màn hình list thử việc.

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: F2_022
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Chọn quản lý trực tiếp'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Chọn quản lý trực tiếp'.
  3. Nhập các thông tin khác hợp lệ.
  4. Nhấn nút 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn quản lý trực tiếp'.

--------------------------------------------------------------------------------

Test Case #23
----------------------------------------
ID: F2_023
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Ngày bắt đầu thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày bắt đầu thử việc'.
  3. Nhập các thông tin khác hợp lệ.
  4. Nhấn nút 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập ngày bắt đầu thử việc'.

--------------------------------------------------------------------------------

Test Case #24
----------------------------------------
ID: F2_024
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Ngày kết thúc thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày kết thúc thử việc'.
  3. Nhập các thông tin khác hợp lệ.
  4. Nhấn nút 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập ngày kết thúc thử việc'.

--------------------------------------------------------------------------------

Test Case #25
----------------------------------------
ID: F2_025
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Chọn người đánh giá' trong giai đoạn
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Tạo một giai đoạn thử việc.
  3. Để trống trường 'Chọn người đánh giá' trong giai đoạn.
  4. Nhập các thông tin khác hợp lệ.
  5. Nhấn nút 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn người đánh giá cho giai đoạn'.

--------------------------------------------------------------------------------

Test Case #26
----------------------------------------
ID: F2_026
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Chọn mẫu đánh giá' trong giai đoạn
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Tạo một giai đoạn thử việc.
  3. Để trống trường 'Chọn mẫu đánh giá' trong giai đoạn.
  4. Nhập các thông tin khác hợp lệ.
  5. Nhấn nút 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn mẫu đánh giá cho giai đoạn'.

--------------------------------------------------------------------------------

Test Case #27
----------------------------------------
ID: F2_027
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Input nhập trọng số'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Tạo một giai đoạn thử việc.
  3. Chọn người đánh giá và mẫu đánh giá.
  4. Click 'Cài đặt trọng số chấm điểm'.
  5. Để trống trường 'Input nhập trọng số'.
  6. Nhập các thông tin khác hợp lệ.
  7. Nhấn nút 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập trọng số chấm điểm'.

--------------------------------------------------------------------------------

Test Case #28
----------------------------------------
ID: F2_028
Mục đích kiểm thử: Kiểm tra validation 'Input nhập trọng số' phải là số nguyên dương
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Tạo một giai đoạn thử việc.
  3. Chọn người đánh giá và mẫu đánh giá.
  4. Click 'Cài đặt trọng số chấm điểm'.
  5. Nhập giá trị không phải số nguyên dương vào trường 'Input nhập trọng số' (ví dụ: -1, 0, 1.5, abc).
  6. Nhập các thông tin khác hợp lệ.
  7. Nhấn nút 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Trọng số chấm điểm phải là số nguyên dương'.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F3_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình 'Chỉnh sửa Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.
  2. Kiểm tra các thành phần giao diện như thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, button Thêm giai đoạn, button Xóa giai đoạn, chọn người đánh giá, chọn mẫu đánh giá, cài đặt trọng số, hạn đánh giá, hạn phê duyệt, chọn kết quả mặc định, gửi thông báo nhắc nhở, button Lưu, button Hủy hiển thị đầy đủ.

Kết quả mong muốn:
  Màn hình hiển thị đầy đủ các thành phần và dữ liệu theo yêu cầu.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F3_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button và trường thông tin
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.
  2. Kiểm tra trạng thái enable/disable của các button 'Lưu' và 'Hủy'.
  3. Kiểm tra trạng thái enable/disable của các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt.

Kết quả mong muốn:
  Các button đều enable, button 'Lưu cập nhật' disable, các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F3_003
Mục đích kiểm thử: Kiểm tra button 'Xóa giai đoạn' chỉ hiện khi chưa
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 45
Generated by Test Case Generator v1.0
