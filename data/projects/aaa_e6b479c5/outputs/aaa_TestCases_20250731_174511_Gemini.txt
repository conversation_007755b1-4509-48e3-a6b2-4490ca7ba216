================================================================================
TEST CASES - AAA
Generated on: 2025-07-31 17:45:11
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F1_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Màn hình hiển thị danh sách thử việc với các cột: Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F1_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Các button đều enable trừ button 'Lưu'/'Lưu cập nhật' disable.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F1_003
Mục đích kiểm thử: Kiểm tra ô input nhập điều kiện tìm kiếm
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập điều kiện tìm kiếm vào ô input 'Bộ lọc'.

Kết quả mong muốn:
  Ô input cho phép nhập điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F1_004
Mục đích kiểm thử: Kiểm tra hiển thị Tên nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Hiển thị Tên nhân viên trong danh sách.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F1_005
Mục đích kiểm thử: Kiểm tra hiển thị Phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Hiển thị phòng ban của nhân viên trong danh sách.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F1_006
Mục đích kiểm thử: Kiểm tra hiển thị Vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Hiển thị vị trí của nhân viên trong danh sách.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F1_007
Mục đích kiểm thử: Kiểm tra hiển thị Hình thức
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Hiển thị hình thức làm việc của nhân viên trong danh sách.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F1_008
Mục đích kiểm thử: Kiểm tra hiển thị Ngày bắt đầu
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Hiển thị ngày bắt đầu thử việc của nhân viên trong danh sách.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F1_009
Mục đích kiểm thử: Kiểm tra hiển thị Ngày kết thúc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Hiển thị ngày kết thúc thử việc của nhân viên trong danh sách.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F1_010
Mục đích kiểm thử: Kiểm tra điều hướng khi click button Chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click button 'Chỉnh sửa' của một nhân viên.

Kết quả mong muốn:
  Điều hướng đến màn hình chỉnh sửa thông tin thử việc của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F1_011
Mục đích kiểm thử: Kiểm tra chức năng lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập vị trí vào ô 'Bộ lọc'.
  4. Nhấn Enter hoặc click button tìm kiếm.

Kết quả mong muốn:
  Danh sách hiển thị chỉ những nhân viên có vị trí trùng với điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F1_012
Mục đích kiểm thử: Kiểm tra chức năng lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập phòng ban vào ô 'Bộ lọc'.
  4. Nhấn Enter hoặc click button tìm kiếm.

Kết quả mong muốn:
  Danh sách hiển thị chỉ những nhân viên có phòng ban trùng với điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F1_013
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên đã có bản ghi thử việc.

Kết quả mong muốn:
  Chuyển đến màn hình 'Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F1_014
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên chưa có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc?'.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F1_015
Mục đích kiểm thử: Kiểm tra click 'Có' trên popup tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Có' trên popup.

Kết quả mong muốn:
  Chuyển đến màn hình 'Tạo Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F1_016
Mục đích kiểm thử: Kiểm tra click 'Hủy' trên popup tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click button 'Hủy' trên popup.

Kết quả mong muốn:
  Popup đóng lại và không có hành động nào khác xảy ra.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F2_001
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm: tên nhân sự, email, phòng ban, vị trí, hình thức làm việc.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F2_002
Mục đích kiểm thử: Kiểm tra ô input 'Chọn quản lý trực tiếp'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép chọn quản lý trực tiếp.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F2_003
Mục đích kiểm thử: Kiểm tra ô input 'Chọn người theo dõi'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép chọn người theo dõi.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F2_004
Mục đích kiểm thử: Kiểm tra ô input 'Ngày bắt đầu thử việc'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép nhập ngày bắt đầu thử việc.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F2_005
Mục đích kiểm thử: Kiểm tra ô input 'Ngày kết thúc thử việc'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép nhập ngày kết thúc thử việc.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F2_006
Mục đích kiểm thử: Kiểm tra hiển thị 'Ngày kết thúc' (readonly)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày kết thúc thử việc.

Kết quả mong muốn:
  Hiển thị ngày kết thúc đã nhập ở mục 'Ngày kết thúc thử việc' (readonly).

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F2_007
Mục đích kiểm thử: Kiểm tra hiển thị 'Ngày bắt đầu' (readonly)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày bắt đầu thử việc.

Kết quả mong muốn:
  Hiển thị ngày bắt đầu đã nhập ở mục 'Ngày bắt đầu thử việc' (readonly).

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F2_008
Mục đích kiểm thử: Kiểm tra button 'Thêm giai đoạn'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị button 'Thêm giai đoạn'.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F2_009
Mục đích kiểm thử: Kiểm tra button 'Xóa giai đoạn'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị button 'Xóa giai đoạn'.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F2_010
Mục đích kiểm thử: Kiểm tra ô 'Chọn người đánh giá' trong giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô cho phép chọn người đánh giá trong giai đoạn.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F2_011
Mục đích kiểm thử: Kiểm tra ô 'Chọn mẫu đánh giá' cho từng người đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô cho phép chọn mẫu đánh giá cho từng người đánh giá.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F2_012
Mục đích kiểm thử: Kiểm tra button 'Cài đặt trọng số chấm điểm'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị button 'Cài đặt trọng số chấm điểm'.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F2_013
Mục đích kiểm thử: Kiểm tra ô input nhập trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép nhập trọng số cho các tiêu chí.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F2_014
Mục đích kiểm thử: Kiểm tra ô input 'Hạn đánh giá'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép nhập hạn đánh giá giai đoạn.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F2_015
Mục đích kiểm thử: Kiểm tra ô input 'Hạn phê duyệt'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép nhập hạn phê duyệt giai đoạn.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F2_016
Mục đích kiểm thử: Kiểm tra ô 'Chọn kết quả mặc định'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị ô cho phép chọn kết quả mặc định phê duyệt cho giai đoạn.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F2_017
Mục đích kiểm thử: Kiểm tra input 'Gửi thông báo nhắc nhở đánh giá trước'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị input cho phép chọn số ngày gửi thông báo nhắc nhở đánh giá trước.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F2_018
Mục đích kiểm thử: Kiểm tra button 'Lưu'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị button 'Lưu'.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F2_019
Mục đích kiểm thử: Kiểm tra button 'Hủy'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị button 'Hủy'.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F2_020
Mục đích kiểm thử: Kiểm tra chọn duy nhất 1 quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Chọn một quản lý trực tiếp.
  3. Cố gắng chọn một quản lý trực tiếp khác.

Kết quả mong muốn:
  Chỉ được phép chọn duy nhất 1 quản lý trực tiếp.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F2_021
Mục đích kiểm thử: Kiểm tra chọn nhiều người theo dõi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Chọn nhiều người theo dõi.

Kết quả mong muốn:
  Được phép chọn nhiều người theo dõi.

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: F2_022
Mục đích kiểm thử: Kiểm tra thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập tên giai đoạn.
  3. Nhấn button 'Thêm giai đoạn'.

Kết quả mong muốn:
  Giai đoạn mới được hiển thị ở phía dưới.

--------------------------------------------------------------------------------

Test Case #23
----------------------------------------
ID: F2_023
Mục đích kiểm thử: Kiểm tra xóa giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn.
  3. Click button 'Xóa' của giai đoạn đó.

Kết quả mong muốn:
  Giai đoạn tương ứng bị xóa.

--------------------------------------------------------------------------------

Test Case #24
----------------------------------------
ID: F2_024
Mục đích kiểm thử: Kiểm tra chọn nhiều người đánh giá cho cùng 1 giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Chọn nhiều người đánh giá cho cùng 1 giai đoạn.

Kết quả mong muốn:
  Được phép chọn nhiều người đánh giá cho cùng 1 giai đoạn.

--------------------------------------------------------------------------------

Test Case #25
----------------------------------------
ID: F2_025
Mục đích kiểm thử: Kiểm tra hiển thị form 'Cài đặt trọng số chấm điểm'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click button 'Cài đặt trọng số chấm điểm'.

Kết quả mong muốn:
  Hiển thị form 'Cài đặt trọng số chấm điểm'.

--------------------------------------------------------------------------------

Test Case #26
----------------------------------------
ID: F2_026
Mục đích kiểm thử: Kiểm tra hiển thị trọng số cho các câu hỏi cố định
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Chọn mẫu đánh giá.
  3. Click button 'Cài đặt trọng số chấm điểm'.

Kết quả mong muốn:
  Hiển thị trọng số cho các câu hỏi cố định 'Đánh giá sự phù hợp với tổ chức' hoặc 'Đánh giá sự phù hợp với công việc', hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn.

--------------------------------------------------------------------------------

Test Case #27
----------------------------------------
ID: F2_027
Mục đích kiểm thử: Kiểm tra chức năng lưu thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập đầy đủ thông tin.
  3. Click button 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và chuyển về màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #28
----------------------------------------
ID: F2_028
Mục đích kiểm thử: Kiểm tra chức năng hủy tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập thông tin.
  3. Click button 'Hủy'.

Kết quả mong muốn:
  Hủy tạo mới thử việc và quay trở lại màn hình danh sách thử việc.

--------------------------------------------------------------------------------

Test Case #29
----------------------------------------
ID: F2_029
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Chọn quản lý trực tiếp'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Chọn quản lý trực tiếp'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn quản lý trực tiếp'.

--------------------------------------------------------------------------------

Test Case #30
----------------------------------------
ID: F2_030
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Ngày bắt đầu thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày bắt đầu thử việc'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập ngày bắt đầu thử việc'.

--------------------------------------------------------------------------------

Test Case #31
----------------------------------------
ID: F2_031
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Ngày kết thúc thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày kết thúc thử việc'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập ngày kết thúc thử việc'.

--------------------------------------------------------------------------------

Test Case #32
----------------------------------------
ID: F2_032
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Chọn người đánh giá'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Chọn người đánh giá'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn người đánh giá'.

--------------------------------------------------------------------------------

Test Case #33
----------------------------------------
ID: F2_033
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Chọn mẫu đánh giá'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Chọn mẫu đánh giá'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn mẫu đánh giá'.

--------------------------------------------------------------------------------

Test Case #34
----------------------------------------
ID: F2_034
Mục đích kiểm thử: Kiểm tra validation bắt buộc cho 'Input nhập trọng số'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Input nhập trọng số'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập trọng số'.

--------------------------------------------------------------------------------

Test Case #35
----------------------------------------
ID: F2_035
Mục đích kiểm thử: Kiểm tra validation cho 'Input nhập trọng số' (Nhập số âm)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập số âm vào trường 'Input nhập trọng số'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Trọng số phải là số nguyên dương'.

--------------------------------------------------------------------------------

Test Case #36
----------------------------------------
ID: F2_036
Mục đích kiểm thử: Kiểm tra validation cho 'Input nhập trọng số' (Nhập số 0)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập số 0 vào trường 'Input nhập trọng số'.
  3. Nhập các thông tin khác.
  4. Click button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Trọng số phải là số nguyên dương'.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F3_001
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu khi loading màn hình chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Các button đều enable, button 'Lưu cập nhật' disable, các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F3_002
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm: tên nhân sự, email, phòng ban, vị trí, hình thức làm việc.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F3_003
Mục đích kiểm thử: Kiểm tra ô input 'Chọn quản lý trực tiếp'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép chọn quản lý trực tiếp.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F3_004
Mục đích kiểm thử: Kiểm tra ô input 'Chọn người theo dõi'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép chọn người theo dõi.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F3_005
Mục đích kiểm thử: Kiểm tra ô input 'Ngày bắt đầu thử việc'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép nhập ngày bắt đầu thử việc.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F3_006
Mục đích kiểm thử: Kiểm tra ô input 'Ngày kết thúc thử việc'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Hiển thị ô input cho phép nhập ngày kết thúc thử việc.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F3_007
Mục đích kiểm thử: Kiểm tra hiển thị 'Ngày kết thúc' (readonly)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.
  2. Nhập ngày kết thúc thử việc.

Kết quả mong muốn:
  Hiển thị ngày kết thúc đã nhập ở mục 'Ngày kết thúc thử việc' (readonly).

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F3_008
Mục đích kiểm thử: Kiểm tra hiển thị
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 60
Generated by Test Case Generator v1.0
