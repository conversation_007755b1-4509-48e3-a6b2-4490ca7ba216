================================================================================
TEST CASES - AAA
Generated on: 2025-07-31 17:51:36
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F1_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Màn hình hiển thị danh sách thử việc với các cột: Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F1_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Các button trên màn hình (ngoại trừ 'Lưu'/'Lưu cập nhật') đều enable.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F1_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập điều kiện tìm kiếm vào ô input 'Bộ lọc'.
  4. Nhấn Enter hoặc click vào icon tìm kiếm.

Kết quả mong muốn:
  Danh sách thử việc hiển thị các bản ghi phù hợp với điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F1_004
Mục đích kiểm thử: Kiểm tra hiển thị thông tin Tên nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Cột 'Tên nhân viên' hiển thị tên của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F1_005
Mục đích kiểm thử: Kiểm tra hiển thị thông tin Phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Cột 'Phòng ban' hiển thị phòng ban của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F1_006
Mục đích kiểm thử: Kiểm tra hiển thị thông tin Vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Cột 'Vị trí' hiển thị vị trí của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F1_007
Mục đích kiểm thử: Kiểm tra hiển thị thông tin Hình thức
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Cột 'Hình thức' hiển thị hình thức làm việc của nhân viên thử việc.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F1_008
Mục đích kiểm thử: Kiểm tra hiển thị thông tin Ngày bắt đầu
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Cột 'Ngày bắt đầu' hiển thị ngày bắt đầu thử việc của nhân viên.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F1_009
Mục đích kiểm thử: Kiểm tra hiển thị thông tin Ngày kết thúc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.

Kết quả mong muốn:
  Cột 'Ngày kết thúc' hiển thị ngày kết thúc thử việc của nhân viên.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F1_010
Mục đích kiểm thử: Kiểm tra chức năng button 'Chỉnh sửa'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào button 'Chỉnh sửa' của một bản ghi thử việc.

Kết quả mong muốn:
  Hệ thống điều hướng đến màn hình 'Chỉnh sửa thông tin thử việc' của bản ghi tương ứng.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F1_011
Mục đích kiểm thử: Kiểm tra chức năng lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập tên vị trí vào ô 'Bộ lọc'.
  4. Nhấn Enter.

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên thử việc có vị trí trùng khớp với giá trị đã nhập.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F1_012
Mục đích kiểm thử: Kiểm tra chức năng lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Nhập tên phòng ban vào ô 'Bộ lọc'.
  4. Nhấn Enter.

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên thử việc thuộc phòng ban trùng khớp với giá trị đã nhập.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F1_013
Mục đích kiểm thử: Kiểm tra chức năng click vào tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên đã có bản ghi thử việc.

Kết quả mong muốn:
  Hệ thống chuyển đến màn hình 'Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F1_014
Mục đích kiểm thử: Kiểm tra chức năng click vào tên nhân viên chưa có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc?'.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F1_015
Mục đích kiểm thử: Kiểm tra chức năng click 'Có' trên popup tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click vào button 'Có' trên popup.

Kết quả mong muốn:
  Hệ thống chuyển đến màn hình 'Tạo Thông tin thử việc' của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F1_016
Mục đích kiểm thử: Kiểm tra chức năng click 'Hủy' trên popup tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống.
  2. Điều hướng đến màn hình 'Danh sách thử việc'.
  3. Click vào tên nhân viên chưa có bản ghi thử việc.
  4. Click vào button 'Hủy' trên popup.

Kết quả mong muốn:
  Popup đóng lại và không có hành động nào khác xảy ra.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F2_001
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn hình tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Các button đều enable trừ button 'Lưu' disable.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F2_002
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm: tên nhân sự, email, phòng ban, vị trí, hình thức làm việc.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F2_003
Mục đích kiểm thử: Kiểm tra chức năng chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Chọn quản lý trực tiếp'.
  3. Chọn một quản lý từ danh sách.

Kết quả mong muốn:
  Quản lý được chọn hiển thị trong ô input 'Chọn quản lý trực tiếp'. Chỉ được phép chọn 1 quản lý.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F2_004
Mục đích kiểm thử: Kiểm tra chức năng chọn người theo dõi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Chọn người theo dõi'.
  3. Chọn một hoặc nhiều người theo dõi từ danh sách.

Kết quả mong muốn:
  Người theo dõi được chọn hiển thị trong ô input 'Chọn người theo dõi'. Được phép chọn nhiều người theo dõi.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F2_005
Mục đích kiểm thử: Kiểm tra chức năng nhập Ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Ngày bắt đầu thử việc'.
  3. Chọn một ngày từ calendar.

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input 'Ngày bắt đầu thử việc'.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F2_006
Mục đích kiểm thử: Kiểm tra chức năng nhập Ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào ô input 'Ngày kết thúc thử việc'.
  3. Chọn một ngày từ calendar.

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input 'Ngày kết thúc thử việc'.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F2_007
Mục đích kiểm thử: Kiểm tra hiển thị Ngày kết thúc (tự động)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày vào ô 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ô 'Ngày kết thúc' hiển thị ngày giống với ngày đã nhập ở ô 'Ngày kết thúc thử việc'.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F2_008
Mục đích kiểm thử: Kiểm tra hiển thị Ngày bắt đầu (tự động)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập ngày vào ô 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ô 'Ngày bắt đầu' hiển thị ngày giống với ngày đã nhập ở ô 'Ngày bắt đầu thử việc'.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F2_009
Mục đích kiểm thử: Kiểm tra chức năng button 'Thêm giai đoạn'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập tên giai đoạn vào ô input.
  3. Click vào button 'Thêm giai đoạn'.

Kết quả mong muốn:
  Một giai đoạn thử việc mới được thêm vào danh sách giai đoạn.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F2_010
Mục đích kiểm thử: Kiểm tra chức năng button 'Xóa giai đoạn'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào button 'Xóa giai đoạn' của giai đoạn vừa thêm.

Kết quả mong muốn:
  Giai đoạn thử việc bị xóa khỏi danh sách.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F2_011
Mục đích kiểm thử: Kiểm tra chức năng chọn người đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Chọn người đánh giá' trong giai đoạn.
  4. Chọn một hoặc nhiều người đánh giá từ danh sách.

Kết quả mong muốn:
  Người đánh giá được chọn hiển thị trong ô input 'Chọn người đánh giá'. Có thể chọn nhiều người đánh giá.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F2_012
Mục đích kiểm thử: Kiểm tra chức năng chọn mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá cho giai đoạn.
  4. Click vào ô input 'Chọn mẫu đánh giá' tương ứng với người đánh giá.
  5. Chọn một mẫu đánh giá từ danh sách.

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô input 'Chọn mẫu đánh giá'.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F2_013
Mục đích kiểm thử: Kiểm tra chức năng button 'Cài đặt trọng số chấm điểm'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  4. Click vào button 'Cài đặt trọng số chấm điểm'.

Kết quả mong muốn:
  Hiển thị form 'Cài đặt trọng số chấm điểm'.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F2_014
Mục đích kiểm thử: Kiểm tra chức năng nhập trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  4. Click vào button 'Cài đặt trọng số chấm điểm'.
  5. Nhập trọng số vào ô input 'Input nhập trọng số' cho các tiêu chí.

Kết quả mong muốn:
  Trọng số được nhập hiển thị trong ô input.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F2_015
Mục đích kiểm thử: Kiểm tra chức năng nhập Hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Hạn đánh giá' trong giai đoạn.
  4. Chọn một ngày từ calendar.

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input 'Hạn đánh giá'.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F2_016
Mục đích kiểm thử: Kiểm tra chức năng nhập Hạn phê duyệt
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Hạn phê duyệt' trong giai đoạn.
  4. Chọn một ngày từ calendar.

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input 'Hạn phê duyệt'.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F2_017
Mục đích kiểm thử: Kiểm tra chức năng chọn kết quả mặc định
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Click vào ô input 'Chọn kết quả mặc định' trong giai đoạn.
  4. Chọn một kết quả (Pass/Fail) từ danh sách.

Kết quả mong muốn:
  Kết quả được chọn hiển thị trong ô input 'Chọn kết quả mặc định'.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F2_018
Mục đích kiểm thử: Kiểm tra chức năng chọn số ngày gửi thông báo nhắc nhở đánh giá trước
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào input 'Gửi thông báo nhắc nhở đánh giá trước'.
  3. Chọn số ngày từ danh sách.

Kết quả mong muốn:
  Số ngày được chọn hiển thị trong input 'Gửi thông báo nhắc nhở đánh giá trước'.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F2_019
Mục đích kiểm thử: Kiểm tra chức năng button 'Lưu'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Nhập đầy đủ thông tin hợp lệ vào các trường bắt buộc.
  3. Click vào button 'Lưu'.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và hệ thống quay trở lại màn hình 'Danh sách thử việc'.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F2_020
Mục đích kiểm thử: Kiểm tra chức năng button 'Hủy'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Click vào button 'Hủy'.

Kết quả mong muốn:
  Hệ thống quay trở lại màn hình 'Danh sách thử việc' mà không lưu bất kỳ thông tin nào.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F2_021
Mục đích kiểm thử: Kiểm tra validation trường 'Chọn quản lý trực tiếp'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Chọn quản lý trực tiếp'.
  3. Nhập các thông tin khác hợp lệ.
  4. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn quản lý trực tiếp'.

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: F2_022
Mục đích kiểm thử: Kiểm tra validation trường 'Ngày bắt đầu thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày bắt đầu thử việc'.
  3. Nhập các thông tin khác hợp lệ.
  4. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn ngày bắt đầu thử việc'.

--------------------------------------------------------------------------------

Test Case #23
----------------------------------------
ID: F2_023
Mục đích kiểm thử: Kiểm tra validation trường 'Ngày kết thúc thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày kết thúc thử việc'.
  3. Nhập các thông tin khác hợp lệ.
  4. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn ngày kết thúc thử việc'.

--------------------------------------------------------------------------------

Test Case #24
----------------------------------------
ID: F2_024
Mục đích kiểm thử: Kiểm tra validation trường 'Chọn người đánh giá'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Để trống trường 'Chọn người đánh giá'.
  4. Nhập các thông tin khác hợp lệ.
  5. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn người đánh giá cho giai đoạn'.

--------------------------------------------------------------------------------

Test Case #25
----------------------------------------
ID: F2_025
Mục đích kiểm thử: Kiểm tra validation trường 'Chọn mẫu đánh giá'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá cho giai đoạn.
  4. Để trống trường 'Chọn mẫu đánh giá'.
  5. Nhập các thông tin khác hợp lệ.
  6. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn mẫu đánh giá cho người đánh giá'.

--------------------------------------------------------------------------------

Test Case #26
----------------------------------------
ID: F2_026
Mục đích kiểm thử: Kiểm tra validation trường 'Input nhập trọng số'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  4. Click vào button 'Cài đặt trọng số chấm điểm'.
  5. Để trống trường 'Input nhập trọng số'.
  6. Nhập các thông tin khác hợp lệ.
  7. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập trọng số cho các tiêu chí'.

--------------------------------------------------------------------------------

Test Case #27
----------------------------------------
ID: F2_027
Mục đích kiểm thử: Kiểm tra validation trường 'Input nhập trọng số' - Nhập số âm
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  4. Click vào button 'Cài đặt trọng số chấm điểm'.
  5. Nhập số âm vào trường 'Input nhập trọng số'.
  6. Nhập các thông tin khác hợp lệ.
  7. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Trọng số phải là số nguyên dương'.

--------------------------------------------------------------------------------

Test Case #28
----------------------------------------
ID: F2_028
Mục đích kiểm thử: Kiểm tra validation trường 'Input nhập trọng số' - Nhập số 0
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn một người đánh giá và mẫu đánh giá cho giai đoạn.
  4. Click vào button 'Cài đặt trọng số chấm điểm'.
  5. Nhập số 0 vào trường 'Input nhập trọng số'.
  6. Nhập các thông tin khác hợp lệ.
  7. Click vào button 'Lưu'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Trọng số phải là số nguyên dương'.

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: F3_001
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn hình chỉnh sửa thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Các button đều enable, button 'Lưu cập nhật' disable, các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F3_002
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc trên màn hình chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự

--------------------------------------------------------------------------------


Total Test Cases Generated: 46
Generated by Test Case Generator v1.0
