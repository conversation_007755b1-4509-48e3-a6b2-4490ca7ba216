================================================================================
TEST CASES - TEST
Generated on: 2025-07-25 11:32:32
================================================================================


============================================================
KIỂM TRA GIAO DIỆN NGƯỜI DÙNG (UI TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình Quản lý mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Màn hình Quản lý mẫu đánh giá hiển thị đầy đủ các thành phần: Bộ lọc, Button Thêm mới, Danh sách mẫu đánh giá (Tên, Phòng ban, Vị trí, Button Chỉnh sửa, Button Xóa)

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới

Kết quả mong muốn:
  Popup Tạo mới mẫu đánh giá hiển thị đầy đủ các trường: Tên mẫu, Phòng áp dụng, Vị trí áp dụng, Ghi chú, Button thêm câu hỏi, Tiêu đề câu hỏi, Dropdown chọn loại câu hỏi, Switch, Button Xóa, Button Hủy, Button Lưu

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_003
Mục đích kiểm thử: Kiểm tra hiển thị popup Chỉnh sửa mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa

Kết quả mong muốn:
  Popup Chỉnh sửa mẫu đánh giá hiển thị đầy đủ các trường: Tên mẫu, Phòng áp dụng, Vị trí áp dụng, Ghi chú, Button thêm câu hỏi, Tiêu đề câu hỏi, Dropdown chọn loại câu hỏi, Switch, Button Xóa, Button Hủy, Button Lưu cập nhật, các trường được tự động điền dữ liệu

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn hình Quản lý mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Mọi input/button đều enable

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_UI_005
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên popup Tạo mới mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới

Kết quả mong muốn:
  Mọi input/button đều enable, trừ button Lưu disable

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_UI_006
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên popup Chỉnh sửa mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa

Kết quả mong muốn:
  Mọi input/button đều enable, trừ button Lưu cập nhật disable

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE DỮ LIỆU ĐẦU VÀO (INPUT VALIDATION TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation trường Tên mẫu (Tạo mới) - Bỏ trống
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Bỏ trống trường Tên mẫu
  6. Nhập các trường còn lại
  7. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng nhập Tên mẫu

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation trường Tên mẫu (Tạo mới) - Nhập quá ký tự cho phép
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Nhập quá 255 ký tự vào trường Tên mẫu
  6. Nhập các trường còn lại
  7. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tên mẫu không được vượt quá 255 ký tự

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra validation trường Tên mẫu (Chỉnh sửa) - Bỏ trống
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa
  5. Xóa hết dữ liệu trong trường Tên mẫu
  6. Nhập các trường còn lại
  7. Click Button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng nhập Tên mẫu

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_VAL_004
Mục đích kiểm thử: Kiểm tra validation trường Tên mẫu (Chỉnh sửa) - Nhập quá ký tự cho phép
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa
  5. Nhập quá 255 ký tự vào trường Tên mẫu
  6. Nhập các trường còn lại
  7. Click Button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tên mẫu không được vượt quá 255 ký tự

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_VAL_005
Mục đích kiểm thử: Kiểm tra validation trường Phòng áp dụng (Tạo mới) - Không chọn
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Bỏ trống trường Phòng áp dụng
  6. Nhập các trường còn lại
  7. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng chọn Phòng áp dụng

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_VAL_006
Mục đích kiểm thử: Kiểm tra validation trường Vị trí áp dụng (Tạo mới) - Không chọn
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Bỏ trống trường Vị trí áp dụng
  6. Nhập các trường còn lại
  7. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng chọn Vị trí áp dụng

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_VAL_007
Mục đích kiểm thử: Kiểm tra validation trường Phòng áp dụng (Chỉnh sửa) - Không chọn
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa
  5. Bỏ trống trường Phòng áp dụng
  6. Nhập các trường còn lại
  7. Click Button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng chọn Phòng áp dụng

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_VAL_008
Mục đích kiểm thử: Kiểm tra validation trường Vị trí áp dụng (Chỉnh sửa) - Không chọn
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa
  5. Bỏ trống trường Vị trí áp dụng
  6. Nhập các trường còn lại
  7. Click Button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng chọn Vị trí áp dụng

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_VAL_009
Mục đích kiểm thử: Kiểm tra validation trường Tiêu đề câu hỏi (Tạo mới) - Bỏ trống
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Nhập Tên mẫu, Phòng áp dụng, Vị trí áp dụng
  6. Click Button thêm câu hỏi
  7. Bỏ trống trường Tiêu đề câu hỏi
  8. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng nhập Tiêu đề câu hỏi

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_VAL_010
Mục đích kiểm thử: Kiểm tra validation trường Tiêu đề câu hỏi (Tạo mới) - Nhập quá ký tự cho phép
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Nhập Tên mẫu, Phòng áp dụng, Vị trí áp dụng
  6. Click Button thêm câu hỏi
  7. Nhập quá 255 ký tự vào trường Tiêu đề câu hỏi
  8. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tiêu đề câu hỏi không được vượt quá 255 ký tự

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_VAL_011
Mục đích kiểm thử: Kiểm tra validation trường Tiêu đề câu hỏi (Chỉnh sửa) - Bỏ trống
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa
  5. Click Button thêm câu hỏi
  6. Bỏ trống trường Tiêu đề câu hỏi
  7. Click Button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng nhập Tiêu đề câu hỏi

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_VAL_012
Mục đích kiểm thử: Kiểm tra validation trường Tiêu đề câu hỏi (Chỉnh sửa) - Nhập quá ký tự cho phép
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa
  5. Click Button thêm câu hỏi
  6. Nhập quá 255 ký tự vào trường Tiêu đề câu hỏi
  7. Click Button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tiêu đề câu hỏi không được vượt quá 255 ký tự

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG TÌM KIẾM (SEARCH FIELD VALIDATION)
============================================================

Test Case #1
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra tìm kiếm theo tên mẫu - Tìm thấy
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Nhập tên mẫu vào ô tìm kiếm
  5. Nhấn Enter

Kết quả mong muốn:
  Hiển thị danh sách mẫu đánh giá có tên tương ứng

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_SEARCH_002
Mục đích kiểm thử: Kiểm tra tìm kiếm theo tên mẫu - Không tìm thấy
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Nhập tên mẫu không tồn tại vào ô tìm kiếm
  5. Nhấn Enter

Kết quả mong muốn:
  Hiển thị thông báo không tìm thấy

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_SEARCH_003
Mục đích kiểm thử: Kiểm tra tìm kiếm theo phòng ban - Tìm thấy
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Chọn phòng ban trong bộ lọc
  5. Nhấn Enter

Kết quả mong muốn:
  Hiển thị danh sách mẫu đánh giá có phòng ban tương ứng

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_SEARCH_004
Mục đích kiểm thử: Kiểm tra tìm kiếm theo phòng ban - Không tìm thấy
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Chọn phòng ban không có mẫu đánh giá nào trong bộ lọc
  5. Nhấn Enter

Kết quả mong muốn:
  Hiển thị thông báo không tìm thấy

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_SEARCH_005
Mục đích kiểm thử: Kiểm tra tìm kiếm theo vị trí - Tìm thấy
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Chọn vị trí trong bộ lọc
  5. Nhấn Enter

Kết quả mong muốn:
  Hiển thị danh sách mẫu đánh giá có vị trí tương ứng

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_SEARCH_006
Mục đích kiểm thử: Kiểm tra tìm kiếm theo vị trí - Không tìm thấy
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Chọn vị trí không có mẫu đánh giá nào trong bộ lọc
  5. Nhấn Enter

Kết quả mong muốn:
  Hiển thị thông báo không tìm thấy

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG (FUNCTIONAL TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra chức năng Thêm mới mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Nhập đầy đủ thông tin vào các trường
  6. Click Button Lưu

Kết quả mong muốn:
  Mẫu đánh giá được thêm mới thành công, hiển thị trong danh sách

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra chức năng Chỉnh sửa mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa của mẫu đánh giá cần sửa
  5. Thay đổi thông tin trong các trường
  6. Click Button Lưu cập nhật

Kết quả mong muốn:
  Mẫu đánh giá được cập nhật thành công, hiển thị trong danh sách

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Kiểm tra chức năng Xóa mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Xóa của mẫu đánh giá cần xóa
  5. Click Button Xóa trong popup xác nhận

Kết quả mong muốn:
  Mẫu đánh giá được xóa thành công, không còn hiển thị trong danh sách

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_FUN_004
Mục đích kiểm thử: Kiểm tra chức năng Hủy thêm mới mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Nhập thông tin vào các trường
  6. Click Button Hủy

Kết quả mong muốn:
  Popup đóng lại, mẫu đánh giá không được thêm mới

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_FUN_005
Mục đích kiểm thử: Kiểm tra chức năng Hủy chỉnh sửa mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa của mẫu đánh giá cần sửa
  5. Thay đổi thông tin trong các trường
  6. Click Button Hủy

Kết quả mong muốn:
  Popup đóng lại, mẫu đánh giá không bị thay đổi

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_FUN_006
Mục đích kiểm thử: Kiểm tra chức năng Thêm câu hỏi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới hoặc Chỉnh sửa
  5. Click Button thêm câu hỏi

Kết quả mong muốn:
  Một câu hỏi mới được thêm vào cuối danh sách câu hỏi

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_007
Mục đích kiểm thử: Kiểm tra chức năng Xóa câu hỏi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới hoặc Chỉnh sửa
  5. Click Button Xóa của câu hỏi cần xóa

Kết quả mong muốn:
  Câu hỏi bị xóa khỏi biểu mẫu

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_008
Mục đích kiểm thử: Kiểm tra chức năng Switch bắt buộc trả lời
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới hoặc Chỉnh sửa
  5. Bật Switch của một câu hỏi
  6. Lưu mẫu đánh giá

Kết quả mong muốn:
  Khi thực hiện đánh giá, câu hỏi đó là bắt buộc phải trả lời

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_009
Mục đích kiểm thử: Kiểm tra hiển thị danh sách phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới hoặc Chỉnh sửa
  5. Click vào dropdown Phòng áp dụng

Kết quả mong muốn:
  Hiển thị danh sách phòng ban từ bảng wk_employee.department

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_010
Mục đích kiểm thử: Kiểm tra hiển thị danh sách vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới hoặc Chỉnh sửa
  5. Click vào dropdown Vị trí áp dụng

Kết quả mong muốn:
  Hiển thị danh sách vị trí từ bảng wk_employee.employment_position

--------------------------------------------------------------------------------



============================================================
KIỂM TRA XỬ LÝ LỖI (ERROR HANDLING TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi thêm mới mẫu đánh giá với tên đã tồn tại
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Nhập tên mẫu đã tồn tại
  6. Nhập các trường còn lại
  7. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tên mẫu đã tồn tại

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_ERR_002
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi xóa mẫu đánh giá không tồn tại
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Thực hiện xóa mẫu đánh giá trực tiếp từ DB
  2. Click Button Xóa trên giao diện

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Không tìm thấy mẫu đánh giá

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_ERR_003
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi mất kết nối DB khi thêm mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Giả lập mất kết nối DB
  2. Click module Thử việc
  3. Chọn tab Setting
  4. Chọn tab Mẫu đánh giá
  5. Click Button Thêm mới
  6. Nhập đầy đủ thông tin
  7. Click Button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Mất kết nối đến cơ sở dữ liệu

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_ERR_004
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi không có quyền Quản lý thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập với user không có quyền Quản lý thử việc
  2. Click module Thử việc
  3. Chọn tab Setting
  4. Chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Bạn không có quyền truy cập chức năng này

--------------------------------------------------------------------------------



============================================================
KIỂM TRA HIỆU NĂNG (PERFORMANCE TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_PERF_001
Mục đích kiểm thử: Kiểm tra thời gian tải màn hình Quản lý mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Thời gian tải màn hình không quá 3 giây

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_PERF_002
Mục đích kiểm thử: Kiểm tra thời gian thêm mới mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Thêm mới
  5. Nhập đầy đủ thông tin
  6. Click Button Lưu

Kết quả mong muốn:
  Thời gian thêm mới không quá 2 giây

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_PERF_003
Mục đích kiểm thử: Kiểm tra thời gian chỉnh sửa mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click Button Chỉnh sửa
  5.

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 41
Generated by Test Case Generator v1.0
