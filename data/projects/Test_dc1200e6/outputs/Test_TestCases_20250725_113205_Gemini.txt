================================================================================
TEST CASES - TEST
Generated on: 2025-07-25 11:32:05
================================================================================


============================================================
KIỂM TRA GIAO DIỆN NGƯỜI DÙNG (UI TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình Quản lý mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Màn hình Quản lý mẫu đánh giá hiển thị đầy đủ các thành phần: Bộ lọc, Button Thêm mới, Danh sách mẫu đánh giá (Tên, Phòng ban, Vị trí, Button Chỉnh sửa, Button Xóa)

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click button Thêm mới

Kết quả mong muốn:
  Popup Tạo mới mẫu đánh giá hiển thị đầy đủ các trường: Tên mẫu, Phòng áp dụng, Vị trí áp dụng, Ghi chú, Button thêm câu hỏi, Tiêu đề câu hỏi, Dropdown chọn loại câu hỏi, Switch, Button Xóa, Button Hủy, Button Lưu

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_003
Mục đích kiểm thử: Kiểm tra hiển thị popup Chỉnh sửa mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá
  4. Click button Chỉnh sửa trên một mẫu đánh giá

Kết quả mong muốn:
  Popup Chỉnh sửa mẫu đánh giá hiển thị đầy đủ các trường: Tên mẫu (đã điền), Phòng áp dụng (đã điền), Vị trí áp dụng (đã điền), Ghi chú (đã điền), Button thêm câu hỏi, Tiêu đề câu hỏi, Dropdown chọn loại câu hỏi, Switch, Button Xóa, Button Hủy, Button Lưu cập nhật

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra trạng thái enable/disable của các button trên màn hình Quản lý mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Mọi input/button đều enable, trừ button Lưu/Lưu cập nhật disable khi chưa điền thông tin

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_UI_005
Mục đích kiểm thử: Kiểm tra hiển thị mặc định của dropdown chọn loại câu hỏi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá hoặc Chỉnh sửa mẫu đánh giá
  2. Kiểm tra dropdown chọn loại câu hỏi

Kết quả mong muốn:
  Dropdown chọn loại câu hỏi hiển thị mặc định là 'Trả lời ngắn'

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_UI_006
Mục đích kiểm thử: Kiểm tra trạng thái mặc định của Switch (bắt buộc trả lời)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá hoặc Chỉnh sửa mẫu đánh giá
  2. Kiểm tra trạng thái của Switch

Kết quả mong muốn:
  Switch hiển thị ở trạng thái tắt (không bắt buộc)

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE DỮ LIỆU ĐẦU VÀO (INPUT VALIDATION TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation trường Tên mẫu (Tạo mới)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Để trống trường Tên mẫu
  3. Nhập các trường khác
  4. Click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập tên mẫu'

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation trường Tên mẫu (Chỉnh sửa)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Xóa nội dung trường Tên mẫu
  3. Click button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập tên mẫu'

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra độ dài tối đa của trường Tên mẫu (Tạo mới)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Nhập Tên mẫu vượt quá 255 ký tự
  3. Click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên mẫu không được vượt quá 255 ký tự' hoặc hệ thống tự động cắt bớt ký tự

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_VAL_004
Mục đích kiểm thử: Kiểm tra độ dài tối đa của trường Tên mẫu (Chỉnh sửa)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Nhập Tên mẫu vượt quá 255 ký tự
  3. Click button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên mẫu không được vượt quá 255 ký tự' hoặc hệ thống tự động cắt bớt ký tự

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_VAL_005
Mục đích kiểm thử: Kiểm tra validation trường Phòng áp dụng (Tạo mới)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Không chọn Phòng áp dụng
  3. Nhập các trường khác
  4. Click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn phòng ban áp dụng'

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_VAL_006
Mục đích kiểm thử: Kiểm tra validation trường Phòng áp dụng (Chỉnh sửa)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Bỏ chọn Phòng áp dụng
  3. Click button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn phòng ban áp dụng'

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_VAL_007
Mục đích kiểm thử: Kiểm tra validation trường Vị trí áp dụng (Tạo mới)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Không chọn Vị trí áp dụng
  3. Nhập các trường khác
  4. Click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn vị trí áp dụng'

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_VAL_008
Mục đích kiểm thử: Kiểm tra validation trường Vị trí áp dụng (Chỉnh sửa)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Bỏ chọn Vị trí áp dụng
  3. Click button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng chọn vị trí áp dụng'

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_VAL_009
Mục đích kiểm thử: Kiểm tra độ dài tối đa của trường Ghi chú (Tạo mới)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Nhập Ghi chú vượt quá giới hạn cho phép
  3. Click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Ghi chú không được vượt quá giới hạn cho phép' hoặc hệ thống tự động cắt bớt ký tự

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_VAL_010
Mục đích kiểm thử: Kiểm tra độ dài tối đa của trường Ghi chú (Chỉnh sửa)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Nhập Ghi chú vượt quá giới hạn cho phép
  3. Click button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Ghi chú không được vượt quá giới hạn cho phép' hoặc hệ thống tự động cắt bớt ký tự

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_VAL_011
Mục đích kiểm thử: Kiểm tra độ dài tối đa của trường Tiêu đề câu hỏi (Tạo mới)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Nhập Tiêu đề câu hỏi vượt quá 255 ký tự
  3. Click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tiêu đề câu hỏi không được vượt quá 255 ký tự' hoặc hệ thống tự động cắt bớt ký tự

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_VAL_012
Mục đích kiểm thử: Kiểm tra độ dài tối đa của trường Tiêu đề câu hỏi (Chỉnh sửa)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Nhập Tiêu đề câu hỏi vượt quá 255 ký tự
  3. Click button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tiêu đề câu hỏi không được vượt quá 255 ký tự' hoặc hệ thống tự động cắt bớt ký tự

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG TÌM KIẾM (SEARCH FIELD VALIDATION)
============================================================

Test Case #1
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra tìm kiếm theo tên mẫu (chính xác)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập tên mẫu chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc button tìm kiếm

Kết quả mong muốn:
  Hiển thị kết quả tìm kiếm là mẫu đánh giá có tên tương ứng

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_SEARCH_002
Mục đích kiểm thử: Kiểm tra tìm kiếm theo tên mẫu (không chính xác)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập tên mẫu không chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc button tìm kiếm

Kết quả mong muốn:
  Hiển thị thông báo 'Không tìm thấy kết quả' hoặc danh sách mẫu đánh giá trống

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_SEARCH_003
Mục đích kiểm thử: Kiểm tra tìm kiếm theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn phòng ban từ bộ lọc
  2. Nhấn Enter hoặc button tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách mẫu đánh giá áp dụng cho phòng ban đã chọn

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_SEARCH_004
Mục đích kiểm thử: Kiểm tra tìm kiếm theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn vị trí từ bộ lọc
  2. Nhấn Enter hoặc button tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách mẫu đánh giá áp dụng cho vị trí đã chọn

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_SEARCH_005
Mục đích kiểm thử: Kiểm tra tìm kiếm kết hợp phòng ban và vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn phòng ban và vị trí từ bộ lọc
  2. Nhấn Enter hoặc button tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách mẫu đánh giá áp dụng cho cả phòng ban và vị trí đã chọn

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_SEARCH_006
Mục đích kiểm thử: Kiểm tra tìm kiếm với ký tự đặc biệt
Độ ưu tiên: Low

Các bước thực hiện:
  1. Nhập ký tự đặc biệt vào ô tìm kiếm
  2. Nhấn Enter hoặc button tìm kiếm

Kết quả mong muốn:
  Hệ thống xử lý tìm kiếm và trả về kết quả hợp lệ hoặc thông báo lỗi

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG (FUNCTIONAL TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra chức năng Thêm mới mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Nhập đầy đủ thông tin hợp lệ
  3. Click button Lưu

Kết quả mong muốn:
  Mẫu đánh giá được tạo thành công và hiển thị trong danh sách

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra chức năng Chỉnh sửa mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một mẫu đánh giá trong danh sách
  2. Click button Chỉnh sửa
  3. Thay đổi thông tin
  4. Click button Lưu cập nhật

Kết quả mong muốn:
  Mẫu đánh giá được cập nhật thành công và hiển thị thông tin mới trong danh sách

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Kiểm tra chức năng Xóa mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một mẫu đánh giá trong danh sách
  2. Click button Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Mẫu đánh giá bị xóa khỏi danh sách và không còn tồn tại trong hệ thống

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_FUN_004
Mục đích kiểm thử: Kiểm tra chức năng Hủy tạo mới mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Nhập thông tin
  3. Click button Hủy

Kết quả mong muốn:
  Popup đóng lại và không có mẫu đánh giá mới nào được tạo

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_FUN_005
Mục đích kiểm thử: Kiểm tra chức năng Hủy chỉnh sửa mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Thay đổi thông tin
  3. Click button Hủy

Kết quả mong muốn:
  Popup đóng lại và thông tin mẫu đánh giá không bị thay đổi

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_FUN_006
Mục đích kiểm thử: Kiểm tra chức năng Thêm câu hỏi (Tạo mới)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Click button Thêm câu hỏi

Kết quả mong muốn:
  Một câu hỏi mới được thêm vào cuối danh sách câu hỏi

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_007
Mục đích kiểm thử: Kiểm tra chức năng Thêm câu hỏi (Chỉnh sửa)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Click button Thêm câu hỏi

Kết quả mong muốn:
  Một câu hỏi mới được thêm vào cuối danh sách câu hỏi

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_008
Mục đích kiểm thử: Kiểm tra chức năng Xóa câu hỏi (Tạo mới)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Thêm một câu hỏi
  3. Click button Xóa trên câu hỏi vừa thêm

Kết quả mong muốn:
  Câu hỏi bị xóa khỏi danh sách câu hỏi

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_009
Mục đích kiểm thử: Kiểm tra chức năng Xóa câu hỏi (Chỉnh sửa)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Click button Xóa trên một câu hỏi

Kết quả mong muốn:
  Câu hỏi bị xóa khỏi danh sách câu hỏi

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_010
Mục đích kiểm thử: Kiểm tra chức năng Switch bắt buộc trả lời
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới/Chỉnh sửa mẫu đánh giá
  2. Bật Switch bắt buộc trả lời cho một câu hỏi
  3. Lưu mẫu đánh giá

Kết quả mong muốn:
  Khi người dùng thực hiện đánh giá, câu hỏi này sẽ yêu cầu phải trả lời trước khi gửi

--------------------------------------------------------------------------------



============================================================
KIỂM TRA XỬ LÝ LỖI (ERROR HANDLING TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi kết nối database bị gián đoạn (Tạo mới)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Giả lập lỗi kết nối database
  3. Nhập thông tin và click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi thân thiện cho người dùng và không lưu dữ liệu

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_ERR_002
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi kết nối database bị gián đoạn (Chỉnh sửa)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Chỉnh sửa mẫu đánh giá
  2. Giả lập lỗi kết nối database
  3. Thay đổi thông tin và click button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị thông báo lỗi thân thiện cho người dùng và không cập nhật dữ liệu

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_ERR_003
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi xóa mẫu đánh giá không tồn tại
Độ ưu tiên: High

Các bước thực hiện:
  1. Thực hiện xóa mẫu đánh giá với ID không tồn tại trong database

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Mẫu đánh giá không tồn tại' hoặc tương tự

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_ERR_004
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi trùng tên mẫu đánh giá (Tạo mới)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Nhập tên mẫu đã tồn tại trong hệ thống
  3. Click button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên mẫu đã tồn tại'

--------------------------------------------------------------------------------



============================================================
KIỂM TRA HIỆU NĂNG (PERFORMANCE TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_PERF_001
Mục đích kiểm thử: Kiểm tra thời gian tải màn hình Quản lý mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc
  2. Chọn tab Setting
  3. Chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Màn hình Quản lý mẫu đánh giá tải trong vòng 3 giây

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_PERF_002
Mục đích kiểm thử: Kiểm tra thời gian tạo mới mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới mẫu đánh giá
  2. Nhập đầy đủ thông tin hợp lệ
  3. Click button Lưu

Kết quả mong muốn:
  Mẫu đánh giá được tạo và hiển thị trong danh sách trong vòng 2 giây

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_PERF_003
Mục đích kiểm thử: Kiểm tra thời gian chỉnh sửa mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn một mẫu đánh giá trong danh sách
  2. Click button Chỉnh sửa
  3. Thay đổi thông tin
  4. Click button Lưu cập nhật

Kết quả mong muốn:
  Mẫu đánh giá được cập nhật và hiển thị thông tin mới trong danh sách trong vòng 2 giây

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_PERF_004
Mục đích kiểm thử: Kiểm tra thời gian xóa mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn một mẫu đánh giá trong danh sách
  2. Click button Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Mẫu đánh giá bị xóa khỏi danh sách trong vòng 1 giây

--------------------------------------------------------------------------------


Total Test Cases Generated: 42
Generated by Test Case Generator v1.0
