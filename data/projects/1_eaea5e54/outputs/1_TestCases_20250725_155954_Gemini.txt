================================================================================
TEST CASES - 1
Generated on: 2025-07-25 15:59:54
================================================================================


============================================================
KIỂM TRA GIAO DIỆN NGƯỜI DÙNG (UI TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Nhấn nút Tạo mới.

Kết quả mong muốn:
  Hiển thị popup Tạo mới chức danh với các trường Tên chức danh, Mô tả và nút Lưu, Hủy.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra hiển thị popup Chỉnh sửa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Chọn một chức danh từ danh sách.
  4. Nhấn nút Sửa.

Kết quả mong muốn:
  Hiển thị popup Chỉnh sửa chức danh với các trường Tên chức danh, Mô tả (pre-fill dữ liệu cũ) và nút Lưu, Hủy.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_003
Mục đích kiểm thử: Kiểm tra hiển thị popup xác nhận Xóa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Chọn một chức danh từ danh sách.
  4. Nhấn nút Xóa.

Kết quả mong muốn:
  Hiển thị popup xác nhận Xóa chức danh với nút Đồng ý và Hủy.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra giao diện ô tìm kiếm chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.

Kết quả mong muốn:
  Hiển thị ô nhập liệu Tìm kiếm trên danh sách chức danh.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_UI_005
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị màn hình danh sách thử việc với các cột: Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa và ô Bộ lọc.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_UI_006
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của button trên màn hình danh sách thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc.

Kết quả mong muốn:
  Các button đều enable trừ button Lưu/Lưu cập nhật disable.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_UI_007
Mục đích kiểm thử: Kiểm tra hiển thị màn hình Tạo Thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo Thử việc.

Kết quả mong muốn:
  Hiển thị màn hình Tạo Thử việc với các trường thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, các giai đoạn thử việc, người đánh giá, mẫu đánh giá, trọng số, hạn đánh giá, hạn phê duyệt, kết quả mặc định, thông báo nhắc nhở và button Lưu, Hủy.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_UI_008
Mục đích kiểm thử: Kiểm tra hiển thị màn hình Chỉnh sửa Thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Chỉnh sửa Thử việc.

Kết quả mong muốn:
  Hiển thị màn hình Chỉnh sửa Thử việc với các trường thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, các giai đoạn thử việc, người đánh giá, mẫu đánh giá, trọng số, hạn đánh giá, hạn phê duyệt, kết quả mặc định, thông báo nhắc nhở và button Lưu, Hủy.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_UI_009
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của button trên màn hình Chỉnh sửa Thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình Chỉnh sửa Thử việc.

Kết quả mong muốn:
  Các button đều enable, btn Lưu cập nhật disable, các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_UI_010
Mục đích kiểm thử: Kiểm tra hiển thị form Cài đặt trọng số chấm điểm
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Nhấn button Cài đặt trọng số chấm điểm.

Kết quả mong muốn:
  Hiển thị form Cài đặt trọng số chấm điểm với các ô input nhập trọng số cho các tiêu chí.

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE DỮ LIỆU ĐẦU VÀO (INPUT VALIDATION TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation Tên chức danh (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến popup Tạo mới chức danh.
  2. Để trống trường Tên chức danh.
  3. Nhập thông tin vào trường Mô tả.
  4. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập Tên chức danh'.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation Tên chức danh (tối đa 64 ký tự)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến popup Tạo mới chức danh.
  2. Nhập Tên chức danh vượt quá 64 ký tự.
  3. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Tên chức danh không được vượt quá 64 ký tự'.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra validation Tên chức danh (không trùng lặp)
Độ ưu tiên: High

Các bước thực hiện:
  1. Tạo một chức danh với Tên 'Nhân viên'.
  2. Điều hướng đến popup Tạo mới chức danh.
  3. Nhập Tên chức danh là 'Nhân viên'.
  4. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Tên chức danh đã tồn tại'.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_VAL_004
Mục đích kiểm thử: Kiểm tra validation Mô tả (tối đa 256 ký tự)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến popup Tạo mới chức danh.
  2. Nhập Mô tả vượt quá 256 ký tự.
  3. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Mô tả không được vượt quá 256 ký tự'.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_VAL_005
Mục đích kiểm thử: Kiểm tra validation Tên chức danh (bắt buộc) khi sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn Sửa.
  3. Xóa hết nội dung trường Tên chức danh.
  4. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập Tên chức danh'.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_VAL_006
Mục đích kiểm thử: Kiểm tra validation Tên chức danh (không trùng lặp) khi sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Tạo 2 chức danh: 'Nhân viên' và 'Quản lý'.
  2. Chọn chức danh 'Nhân viên' và nhấn Sửa.
  3. Sửa Tên chức danh thành 'Quản lý'.
  4. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Tên chức danh đã tồn tại'.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_VAL_007
Mục đích kiểm thử: Kiểm tra validation Ngày bắt đầu thử việc (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo Thử việc.
  2. Để trống trường Ngày bắt đầu thử việc.
  3. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập Ngày bắt đầu thử việc'.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_VAL_008
Mục đích kiểm thử: Kiểm tra validation Ngày kết thúc thử việc (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo Thử việc.
  2. Để trống trường Ngày kết thúc thử việc.
  3. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập Ngày kết thúc thử việc'.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_VAL_009
Mục đích kiểm thử: Kiểm tra validation Chọn quản lý trực tiếp (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo Thử việc.
  2. Để trống trường Chọn quản lý trực tiếp.
  3. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng chọn Quản lý trực tiếp'.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_VAL_010
Mục đích kiểm thử: Kiểm tra validation Chọn người đánh giá (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo Thử việc.
  2. Thêm một giai đoạn thử việc.
  3. Để trống trường Chọn người đánh giá.
  4. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng chọn Người đánh giá'.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_VAL_011
Mục đích kiểm thử: Kiểm tra validation Chọn mẫu đánh giá (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo Thử việc.
  2. Thêm một giai đoạn thử việc.
  3. Chọn người đánh giá.
  4. Để trống trường Chọn mẫu đánh giá.
  5. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng chọn Mẫu đánh giá'.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_VAL_012
Mục đích kiểm thử: Kiểm tra validation Input nhập trọng số (bắt buộc, nguyên dương)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo Thử việc.
  2. Thêm một giai đoạn thử việc.
  3. Chọn người đánh giá và mẫu đánh giá.
  4. Nhấn Cài đặt trọng số chấm điểm.
  5. Để trống hoặc nhập giá trị không phải số nguyên dương vào Input nhập trọng số.
  6. Nhấn Lưu.

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập trọng số là số nguyên dương'.

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG TÌM KIẾM (SEARCH FIELD VALIDATION)
============================================================

Test Case #1
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh theo tên (đúng)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Nhập một phần tên chức danh đã tồn tại vào ô Tìm kiếm.

Kết quả mong muốn:
  Danh sách chức danh hiển thị các chức danh có tên chứa từ khóa tìm kiếm.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_SEARCH_002
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh theo tên (sai)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Nhập một tên chức danh không tồn tại vào ô Tìm kiếm.

Kết quả mong muốn:
  Danh sách chức danh không hiển thị kết quả nào hoặc hiển thị thông báo 'Không tìm thấy kết quả'.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_SEARCH_003
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh theo tên (không phân biệt hoa thường)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Nhập từ khóa tìm kiếm với ký tự hoa thường lẫn lộn.

Kết quả mong muốn:
  Danh sách chức danh hiển thị các chức danh có tên chứa từ khóa tìm kiếm (không phân biệt hoa thường).

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_SEARCH_004
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh theo tên (ký tự đặc biệt)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Nhập ký tự đặc biệt vào ô Tìm kiếm.

Kết quả mong muốn:
  Hệ thống xử lý tìm kiếm và hiển thị kết quả phù hợp (tùy theo yêu cầu).

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG (FUNCTIONAL TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra chức năng Tạo mới chức danh (thành công)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến popup Tạo mới chức danh.
  2. Nhập đầy đủ thông tin hợp lệ vào các trường.
  3. Nhấn Lưu.

Kết quả mong muốn:
  Chức danh được thêm vào hệ thống thành công và hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra chức năng Tạo mới chức danh (hủy)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến popup Tạo mới chức danh.
  2. Nhập thông tin vào các trường.
  3. Nhấn Hủy.

Kết quả mong muốn:
  Popup đóng lại và chức danh không được thêm vào hệ thống.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Kiểm tra chức năng Sửa chức danh (thành công)
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn Sửa.
  3. Chỉnh sửa thông tin trong popup Chỉnh sửa chức danh.
  4. Nhấn Lưu.

Kết quả mong muốn:
  Chức danh được cập nhật thành công và hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_FUN_004
Mục đích kiểm thử: Kiểm tra chức năng Sửa chức danh (hủy)
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn Sửa.
  3. Chỉnh sửa thông tin trong popup Chỉnh sửa chức danh.
  4. Nhấn Hủy.

Kết quả mong muốn:
  Popup đóng lại và chức danh không được cập nhật.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_FUN_005
Mục đích kiểm thử: Kiểm tra chức năng Xóa chức danh (thành công)
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách (chưa được gán cho nhân viên).
  2. Nhấn Xóa.
  3. Nhấn Đồng ý trong popup xác nhận.

Kết quả mong muốn:
  Chức danh được xóa khỏi hệ thống thành công và hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_FUN_006
Mục đích kiểm thử: Kiểm tra chức năng Xóa chức danh (hủy)
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn Xóa.
  3. Nhấn Hủy trong popup xác nhận.

Kết quả mong muốn:
  Popup đóng lại và chức danh không bị xóa.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_007
Mục đích kiểm thử: Kiểm tra chức năng Xóa chức danh (không cho phép xóa nếu đang được gán cho nhân viên)
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách (đang được gán cho nhân viên).
  2. Nhấn Xóa.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Không thể xóa chức danh này vì đang được gán cho nhân viên'.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_008
Mục đích kiểm thử: Kiểm tra điều hướng đến màn hình Thông tin thử việc khi click tên nhân viên (đã có bản ghi thử việc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc.
  2. Click vào tên nhân viên đã có bản ghi thử việc.

Kết quả mong muốn:
  Hệ thống điều hướng đến màn hình Thông tin thử việc của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_009
Mục đích kiểm thử: Kiểm tra hiển thị popup tạo Thông tin Thử việc khi click tên nhân viên (chưa có bản ghi thử việc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc.
  2. Click vào tên nhân viên chưa có bản ghi thử việc.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc?'.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_010
Mục đích kiểm thử: Kiểm tra điều hướng đến màn hình Tạo Thông tin Thử việc khi click 'Có' trong popup
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc.
  2. Click vào tên nhân viên chưa có bản ghi thử việc.
  3. Nhấn button 'Có' trong popup.

Kết quả mong muốn:
  Hệ thống điều hướng đến màn hình Tạo Thông tin Thử việc.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_FUN_011
Mục đích kiểm thử: Kiểm tra đóng popup khi click 'Hủy' trong popup
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc.
  2. Click vào tên nhân viên chưa có bản ghi thử việc.
  3. Nhấn button 'Hủy' trong popup.

Kết quả mong muốn:
  Popup đóng lại.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_FUN_012
Mục đích kiểm thử: Kiểm tra chức năng thêm giai đoạn thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Nhập tên giai đoạn vào ô input.
  3. Nhấn button Thêm giai đoạn.

Kết quả mong muốn:
  Giai đoạn mới được thêm vào danh sách giai đoạn thử việc.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_FUN_013
Mục đích kiểm thử: Kiểm tra chức năng xóa giai đoạn thử việc (chưa có kết quả đánh giá/phê duyệt)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Thêm một giai đoạn thử việc.
  3. Nhấn button Xóa giai đoạn tương ứng.

Kết quả mong muốn:
  Giai đoạn thử việc bị xóa khỏi danh sách.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: TC_FUN_014
Mục đích kiểm thử: Kiểm tra chức năng không cho phép xóa giai đoạn thử việc (đã có kết quả đánh giá/phê duyệt)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Chỉnh sửa Thử việc (giai đoạn đã có kết quả đánh giá/phê duyệt).
  2. Kiểm tra button Xóa giai đoạn.

Kết quả mong muốn:
  Button Xóa giai đoạn không hiển thị hoặc bị disable.

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: TC_FUN_015
Mục đích kiểm thử: Kiểm tra chức năng lưu thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Nhập đầy đủ thông tin hợp lệ.
  3. Nhấn button Lưu.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: TC_FUN_016
Mục đích kiểm thử: Kiểm tra chức năng hủy tạo/chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Nhập thông tin vào các trường.
  3. Nhấn button Hủy.

Kết quả mong muốn:
  Hệ thống quay trở lại màn hình danh sách thử việc và thông tin không được lưu.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: TC_FUN_017
Mục đích kiểm thử: Kiểm tra chỉ được chọn 1 quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Chọn một quản lý trực tiếp.
  3. Thử chọn một quản lý trực tiếp khác.

Kết quả mong muốn:
  Hệ thống chỉ cho phép chọn 1 quản lý trực tiếp.

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: TC_FUN_018
Mục đích kiểm thử: Kiểm tra được chọn nhiều người theo dõi
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Chọn nhiều người theo dõi.

Kết quả mong muốn:
  Hệ thống cho phép chọn nhiều người theo dõi.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: TC_FUN_019
Mục đích kiểm thử: Kiểm tra hiển thị trọng số chấm điểm khi chọn mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Thêm một giai đoạn thử việc.
  3. Chọn người đánh giá và mẫu đánh giá.
  4. Nhấn Cài đặt trọng số chấm điểm.

Kết quả mong muốn:
  Hiển thị form Cài đặt trọng số chấm điểm với các câu hỏi phù hợp với mẫu đánh giá đã chọn.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: TC_FUN_020
Mục đích kiểm thử: Kiểm tra tự động phê duyệt giai đoạn khi hết hạn phê duyệt (chọn Pass)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Thêm một giai đoạn thử việc.
  3. Chọn kết quả mặc định là Pass.
  4. Đặt hạn phê duyệt đã qua.
  5. Chờ hệ thống tự động phê duyệt.

Kết quả mong muốn:
  Hệ thống tự động phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: TC_FUN_021
Mục đích kiểm thử: Kiểm tra tự động phê duyệt giai đoạn khi hết hạn phê duyệt (chọn Fail)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình Tạo/Chỉnh sửa Thử việc.
  2. Thêm một giai đoạn thử việc.
  3. Chọn kết quả mặc định là Fail.
  4. Đặt hạn phê duyệt

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 47
Generated by Test Case Generator v1.0
