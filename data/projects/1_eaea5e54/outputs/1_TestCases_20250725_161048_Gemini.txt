================================================================================
TEST CASES - 1
Generated on: 2025-07-25 16:10:48
================================================================================


============================================================
KIỂM TRA GIAO DIỆN NGƯỜI DÙNG (UI TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra giao diện màn hình 'Tạo mới chức danh'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Nhấn nút 'Tạo mới'

Kết quả mong muốn:
  Hiển thị popup 'Tạo mới chức danh' với các trường 'Tên chức danh', 'Mô tả' và nút 'Lưu', 'Hủy'

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra giao diện màn hình 'Chỉnh sửa chức danh'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Chọn một chức danh từ danh sách.
  4. Nhấn nút 'Sửa'

Kết quả mong muốn:
  Hiển thị popup 'Chỉnh sửa chức danh' với các trường 'Tên chức danh', 'Mô tả' (pre-fill dữ liệu cũ) và nút 'Lưu', 'Hủy'

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_003
Mục đích kiểm thử: Kiểm tra giao diện popup xác nhận xóa chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.
  3. Chọn một chức danh từ danh sách.
  4. Nhấn nút 'Xóa'

Kết quả mong muốn:
  Hiển thị popup xác nhận xóa chức danh với thông báo xác nhận và nút 'Đồng ý', 'Hủy'

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra giao diện ô tìm kiếm chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.

Kết quả mong muốn:
  Hiển thị ô nhập 'Tìm kiếm' trên danh sách chức danh

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_UI_005
Mục đích kiểm thử: Kiểm tra giao diện màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị các cột: Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa, Ô input tìm kiếm

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_UI_006
Mục đích kiểm thử: Kiểm tra giao diện màn hình 'Tạo Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.

Kết quả mong muốn:
  Hiển thị các trường thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, các button Thêm/Xóa giai đoạn, chọn người đánh giá, chọn mẫu đánh giá, cài đặt trọng số, hạn đánh giá, hạn phê duyệt, chọn kết quả mặc định, gửi thông báo nhắc nhở, button Lưu/Hủy

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_UI_007
Mục đích kiểm thử: Kiểm tra giao diện màn hình 'Chỉnh sửa Thử việc'
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Chỉnh sửa Thử việc'.

Kết quả mong muốn:
  Hiển thị các trường thông tin nhân sự, chọn quản lý trực tiếp, chọn người theo dõi, ngày bắt đầu, ngày kết thúc, các button Thêm/Xóa giai đoạn, chọn người đánh giá, chọn mẫu đánh giá, cài đặt trọng số, hạn đánh giá, hạn phê duyệt, chọn kết quả mặc định, gửi thông báo nhắc nhở, button Lưu/Hủy. Các trường thông tin đã có kết quả đánh giá/phê duyệt thì disable

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_UI_008
Mục đích kiểm thử: Kiểm tra hiển thị button 'Xóa giai đoạn'
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo/Chỉnh sửa Thử việc'.
  2. Kiểm tra các giai đoạn thử việc.

Kết quả mong muốn:
  Button 'Xóa giai đoạn' chỉ hiển thị ở những giai đoạn nào chưa có kết quả đánh giá/phê duyệt

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE DỮ LIỆU ĐẦU VÀO (INPUT VALIDATION TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation trường 'Tên chức danh' (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup 'Tạo mới chức danh'.
  2. Để trống trường 'Tên chức danh'.
  3. Nhập thông tin vào trường 'Mô tả' (nếu có).
  4. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập Tên chức danh'

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation trường 'Tên chức danh' (tối đa 64 ký tự)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup 'Tạo mới chức danh'.
  2. Nhập vào trường 'Tên chức danh' một chuỗi có 65 ký tự.
  3. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Tên chức danh không được vượt quá 64 ký tự'

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra validation trường 'Tên chức danh' (không trùng lặp)
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup 'Tạo mới chức danh'.
  2. Nhập vào trường 'Tên chức danh' một giá trị đã tồn tại trong hệ thống.
  3. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Tên chức danh đã tồn tại'

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_VAL_004
Mục đích kiểm thử: Kiểm tra validation trường 'Mô tả' (tối đa 256 ký tự)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup 'Tạo mới chức danh'.
  2. Nhập vào trường 'Mô tả' một chuỗi có 257 ký tự.
  3. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Mô tả không được vượt quá 256 ký tự'

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_VAL_005
Mục đích kiểm thử: Kiểm tra validation trường 'Tên chức danh' khi sửa (trùng lặp)
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh để sửa.
  2. Mở popup 'Chỉnh sửa chức danh'.
  3. Thay đổi 'Tên chức danh' thành một giá trị đã tồn tại (trừ chức danh đang sửa).
  4. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Tên chức danh đã tồn tại'

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_VAL_006
Mục đích kiểm thử: Kiểm tra validation trường 'Chọn quản lý trực tiếp' (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Chọn quản lý trực tiếp'.
  3. Nhập các thông tin khác.
  4. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng chọn quản lý trực tiếp'

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_VAL_007
Mục đích kiểm thử: Kiểm tra validation trường 'Ngày bắt đầu thử việc' (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày bắt đầu thử việc'.
  3. Nhập các thông tin khác.
  4. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập Ngày bắt đầu thử việc'

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_VAL_008
Mục đích kiểm thử: Kiểm tra validation trường 'Ngày kết thúc thử việc' (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Để trống trường 'Ngày kết thúc thử việc'.
  3. Nhập các thông tin khác.
  4. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập Ngày kết thúc thử việc'

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_VAL_009
Mục đích kiểm thử: Kiểm tra validation trường 'Chọn người đánh giá' (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Để trống trường 'Chọn người đánh giá'.
  4. Nhập các thông tin khác.
  5. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng chọn người đánh giá'

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_VAL_010
Mục đích kiểm thử: Kiểm tra validation trường 'Chọn mẫu đánh giá' (bắt buộc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn người đánh giá.
  4. Để trống trường 'Chọn mẫu đánh giá'.
  5. Nhập các thông tin khác.
  6. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng chọn mẫu đánh giá'

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_VAL_011
Mục đích kiểm thử: Kiểm tra validation trường 'Input nhập trọng số' (bắt buộc, nguyên dương)
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo Thử việc'.
  2. Thêm một giai đoạn thử việc.
  3. Chọn người đánh giá và mẫu đánh giá.
  4. Nhấn button 'Cài đặt trọng số chấm điểm'.
  5. Để trống hoặc nhập giá trị không phải số nguyên dương vào trường 'Input nhập trọng số'.
  6. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị lỗi 'Vui lòng nhập trọng số là số nguyên dương'

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG TÌM KIẾM (SEARCH FIELD VALIDATION)
============================================================

Test Case #1
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh (tên chức danh)
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập một từ khóa vào ô 'Tìm kiếm' trên danh sách chức danh.
  2. Kiểm tra danh sách kết quả.

Kết quả mong muốn:
  Hệ thống hiển thị danh sách chức danh có tên chứa từ khóa đã nhập (không phân biệt hoa thường)

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_SEARCH_002
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh (không tìm thấy)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập một từ khóa không tồn tại trong tên chức danh vào ô 'Tìm kiếm' trên danh sách chức danh.
  2. Kiểm tra danh sách kết quả.

Kết quả mong muốn:
  Hệ thống hiển thị thông báo 'Không tìm thấy kết quả' hoặc danh sách trống

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_SEARCH_003
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh (ký tự đặc biệt)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập ký tự đặc biệt vào ô 'Tìm kiếm' trên danh sách chức danh.
  2. Kiểm tra danh sách kết quả.

Kết quả mong muốn:
  Hệ thống hiển thị danh sách chức danh phù hợp hoặc thông báo lỗi (tùy theo yêu cầu)

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG (FUNCTIONAL TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Tạo mới chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup 'Tạo mới chức danh'.
  2. Nhập thông tin hợp lệ vào trường 'Tên chức danh' và 'Mô tả' (nếu có).
  3. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị thông báo 'Tạo mới chức danh thành công' và chức danh mới được thêm vào danh sách

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Hủy tạo mới chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup 'Tạo mới chức danh'.
  2. Nhập thông tin vào các trường.
  3. Nhấn nút 'Hủy'

Kết quả mong muốn:
  Popup đóng lại và không có chức danh mới nào được tạo

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Sửa chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút 'Sửa'.
  3. Chỉnh sửa thông tin trong popup 'Chỉnh sửa chức danh'.
  4. Nhấn nút 'Lưu'

Kết quả mong muốn:
  Hiển thị thông báo 'Cập nhật chức danh thành công' và thông tin chức danh được cập nhật trong danh sách

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_FUN_004
Mục đích kiểm thử: Hủy sửa chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút 'Sửa'.
  3. Chỉnh sửa thông tin trong popup 'Chỉnh sửa chức danh'.
  4. Nhấn nút 'Hủy'

Kết quả mong muốn:
  Popup đóng lại và thông tin chức danh không thay đổi

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_FUN_005
Mục đích kiểm thử: Xóa chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút 'Xóa'.
  3. Nhấn nút 'Đồng ý' trong popup xác nhận.

Kết quả mong muốn:
  Hiển thị thông báo 'Xóa chức danh thành công' và chức danh bị xóa khỏi danh sách

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_FUN_006
Mục đích kiểm thử: Hủy xóa chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút 'Xóa'.
  3. Nhấn nút 'Hủy' trong popup xác nhận.

Kết quả mong muốn:
  Popup đóng lại và chức danh không bị xóa

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_007
Mục đích kiểm thử: Không cho phép xóa chức danh đang được gán cho nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh đang được gán cho nhân viên.
  2. Nhấn nút 'Xóa'.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Không thể xóa chức danh này vì đang được sử dụng'

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_008
Mục đích kiểm thử: Click vào tên nhân viên (đã có bản ghi thử việc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào tên nhân viên đã có bản ghi thử việc trên màn hình danh sách thử việc.

Kết quả mong muốn:
  Chuyển đến màn Thông tin thử việc.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_009
Mục đích kiểm thử: Click vào tên nhân viên (chưa có bản ghi thử việc)
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào tên nhân viên chưa có bản ghi thử việc trên màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiện popup 'Bạn có muốn tạo Thông tin Thử việc'. Click Button Có => chuyển đến màn tạo Thông tin thử việc. Click btn Hủy => Đóng popup

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_010
Mục đích kiểm thử: Thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo/Chỉnh sửa Thử việc'.
  2. Nhập tên giai đoạn.
  3. Nhấn btn Thêm giai đoạn.

Kết quả mong muốn:
  Giai đoạn mới được hiển thị ở phía dưới.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_FUN_011
Mục đích kiểm thử: Xóa giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình 'Tạo/Chỉnh sửa Thử việc'.
  2. Click btn Xóa giai đoạn.

Kết quả mong muốn:
  Giai đoạn tương ứng sẽ bị xóa.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_FUN_012
Mục đích kiểm thử: Lưu thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập đầy đủ thông tin trên màn hình 'Tạo/Chỉnh sửa Thử việc'.
  2. Nhấn btn Lưu.

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_FUN_013
Mục đích kiểm thử: Hủy tạo/chỉnh sửa thông tin thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập thông tin trên màn hình 'Tạo/Chỉnh sửa Thử việc'.
  2. Nhấn btn Hủy.

Kết quả mong muốn:
  Hủy tạo/chỉnh sửa thông tin thử việc , quay trở lại màn hình list thử việc.

--------------------------------------------------------------------------------



============================================================
KIỂM TRA XỬ LÝ LỖI (ERROR HANDLING TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Xử lý lỗi khi kết nối database bị gián đoạn (tạo mới chức danh)
Độ ưu tiên: High

Các bước thực hiện:
  1. Giả lập lỗi kết nối database.
  2. Thực hiện tạo mới chức danh.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Không thể kết nối đến database' hoặc thông báo lỗi chung

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_ERR_002
Mục đích kiểm thử: Xử lý lỗi khi kết nối database bị gián đoạn (sửa chức danh)
Độ ưu tiên: High

Các bước thực hiện:
  1. Giả lập lỗi kết nối database.
  2. Thực hiện sửa chức danh.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Không thể kết nối đến database' hoặc thông báo lỗi chung

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_ERR_003
Mục đích kiểm thử: Xử lý lỗi khi kết nối database bị gián đoạn (xóa chức danh)
Độ ưu tiên: High

Các bước thực hiện:
  1. Giả lập lỗi kết nối database.
  2. Thực hiện xóa chức danh.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Không thể kết nối đến database' hoặc thông báo lỗi chung

--------------------------------------------------------------------------------



============================================================
KIỂM TRA HIỆU NĂNG (PERFORMANCE TESTING)
============================================================

Test Case #1
----------------------------------------
ID: TC_PERF_001
Mục đích kiểm thử: Kiểm tra thời gian tải màn hình danh sách chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến phân hệ Quản trị hệ thống.
  2. Chọn chức năng Quản lý chức danh.

Kết quả mong muốn:
  Màn hình danh sách chức danh tải trong vòng 3 giây

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_PERF_002
Mục đích kiểm thử: Kiểm tra thời gian tìm kiếm chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập từ khóa vào ô 'Tìm kiếm' trên danh sách chức danh.
  2. Đo thời gian hiển thị kết quả.

Kết quả mong muốn:
  Kết quả tìm kiếm hiển thị trong vòng 2 giây

--------------------------------------------------------------------------------


Total Test Cases Generated: 40
Generated by Test Case Generator v1.0
