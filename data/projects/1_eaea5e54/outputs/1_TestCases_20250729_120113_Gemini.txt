================================================================================
TEST CASES - 1
Generated on: 2025-07-29 12:01:13
================================================================================

Test Case #1
----------------------------------------
ID: F1_001
Mục đích kiểm thử: Kiểm tra tạo mới chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. T<PERSON>y cập màn hình Quản lý chức danh.
  2. Nhấn nút Tạo mới.
  3. <PERSON><PERSON>ập Tê<PERSON> chức danh hợp lệ (ví dụ: 'Nhân viên kinh doanh').
  4. Nhập Mô tả (ví dụ: 'Thực hiện các hoạt động kinh doanh').
  5. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo 'Tạo mới chức danh thành công'.
  Chức danh mới được hiển thị trong danh sách chức danh.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: F1_002
Mục đích kiểm thử: Kiểm tra tạo mới chức danh với tên chức danh trùng lặp
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhấn nút Tạo mới.
  3. Nhập Tên chức danh đã tồn tại (ví dụ: 'Nhân viên kinh doanh').
  4. Nhập Mô tả (ví dụ: 'Thực hiện các hoạt động kinh doanh').
  5. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên chức danh đã tồn tại'.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: F1_003
Mục đích kiểm thử: Kiểm tra tạo mới chức danh với tên chức danh vượt quá 64 ký tự
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhấn nút Tạo mới.
  3. Nhập Tên chức danh vượt quá 64 ký tự.
  4. Nhập Mô tả (ví dụ: 'Thực hiện các hoạt động kinh doanh').
  5. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên chức danh không được vượt quá 64 ký tự'.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: F1_004
Mục đích kiểm thử: Kiểm tra tạo mới chức danh với mô tả vượt quá 256 ký tự
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhấn nút Tạo mới.
  3. Nhập Tên chức danh hợp lệ (ví dụ: 'Trưởng phòng kinh doanh').
  4. Nhập Mô tả vượt quá 256 ký tự.
  5. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Mô tả không được vượt quá 256 ký tự'.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: F1_005
Mục đích kiểm thử: Kiểm tra tạo mới chức danh khi bỏ trống trường Tên chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhấn nút Tạo mới.
  3. Bỏ trống trường Tên chức danh.
  4. Nhập Mô tả (ví dụ: 'Thực hiện các hoạt động kinh doanh').
  5. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập Tên chức danh'.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: F1_006
Mục đích kiểm thử: Kiểm tra tạo mới chức danh khi nhấn nút Hủy
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhấn nút Tạo mới.
  3. Nhập Tên chức danh hợp lệ (ví dụ: 'Nhân viên marketing').
  4. Nhập Mô tả (ví dụ: 'Thực hiện các hoạt động marketing').
  5. Nhấn nút Hủy.

Kết quả mong muốn:
  Popup tạo mới chức danh đóng lại.
  Chức danh mới không được tạo trong hệ thống.

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: F1_007
Mục đích kiểm thử: Kiểm tra sửa chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Chọn một chức danh từ danh sách.
  3. Nhấn nút Sửa.
  4. Chỉnh sửa Tên chức danh (ví dụ: 'Nhân viên marketing' thành 'Chuyên viên marketing').
  5. Chỉnh sửa Mô tả (ví dụ: 'Thực hiện các hoạt động marketing' thành 'Lập kế hoạch và thực hiện các hoạt động marketing').
  6. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo 'Cập nhật chức danh thành công'.
  Thông tin chức danh được cập nhật trong danh sách chức danh.

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: F1_008
Mục đích kiểm thử: Kiểm tra sửa chức danh với tên chức danh trùng lặp (trừ chức danh đang sửa)
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Chọn một chức danh từ danh sách.
  3. Nhấn nút Sửa.
  4. Chỉnh sửa Tên chức danh thành tên của một chức danh đã tồn tại khác (ví dụ: 'Nhân viên kinh doanh').
  5. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên chức danh đã tồn tại'.

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: F1_009
Mục đích kiểm thử: Kiểm tra sửa chức danh khi nhấn nút Hủy
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Chọn một chức danh từ danh sách.
  3. Nhấn nút Sửa.
  4. Chỉnh sửa Tên chức danh (ví dụ: 'Nhân viên marketing' thành 'Chuyên viên marketing').
  5. Nhấn nút Hủy.

Kết quả mong muốn:
  Popup chỉnh sửa chức danh đóng lại.
  Thông tin chức danh không thay đổi trong danh sách chức danh.

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: F1_010
Mục đích kiểm thử: Kiểm tra xóa chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Chọn một chức danh từ danh sách (chức danh không được gán cho nhân viên nào).
  3. Nhấn nút Xóa.
  4. Xác nhận xóa bằng cách nhấn nút Đồng ý trong popup xác nhận.

Kết quả mong muốn:
  Hiển thị thông báo 'Xóa chức danh thành công'.
  Chức danh bị xóa khỏi danh sách chức danh.

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: F1_011
Mục đích kiểm thử: Kiểm tra xóa chức danh khi chức danh đang được gán cho nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Chọn một chức danh từ danh sách (chức danh đang được gán cho nhân viên).
  3. Nhấn nút Xóa.

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Không thể xóa chức danh này vì đang được gán cho nhân viên'.

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: F1_012
Mục đích kiểm thử: Kiểm tra xóa chức danh khi nhấn nút Hủy trong popup xác nhận
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Chọn một chức danh từ danh sách.
  3. Nhấn nút Xóa.
  4. Hủy xóa bằng cách nhấn nút Hủy trong popup xác nhận.

Kết quả mong muốn:
  Popup xác nhận đóng lại.
  Chức danh không bị xóa khỏi danh sách chức danh.

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: F1_013
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh theo tên (chính xác)
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhập tên chức danh chính xác vào ô tìm kiếm (ví dụ: 'Nhân viên kinh doanh').

Kết quả mong muốn:
  Danh sách chức danh được lọc và chỉ hiển thị chức danh có tên trùng khớp.

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: F1_014
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh theo tên (không phân biệt hoa thường)
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhập tên chức danh vào ô tìm kiếm với ký tự hoa thường lẫn lộn (ví dụ: 'nHaN vIeN kInH dOaNh').

Kết quả mong muốn:
  Danh sách chức danh được lọc và chỉ hiển thị chức danh có tên trùng khớp (không phân biệt hoa thường).

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: F1_015
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh theo tên (không tìm thấy)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Nhập một từ khóa không tồn tại trong tên chức danh vào ô tìm kiếm (ví dụ: 'Kế toán').

Kết quả mong muốn:
  Danh sách chức danh hiển thị thông báo 'Không tìm thấy kết quả'.

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: F1_016
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh khi ô tìm kiếm trống
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình Quản lý chức danh.
  2. Để trống ô tìm kiếm.

Kết quả mong muốn:
  Hiển thị toàn bộ danh sách chức danh.

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: F2_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị danh sách nhân viên thử việc với đầy đủ thông tin: Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa. Các button đều enable trừ button Lưu/Lưu cập nhật disable

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: F2_002
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.
  2. Chọn một vị trí từ bộ lọc vị trí.
  3. Nhấn Áp dụng.

Kết quả mong muốn:
  Danh sách hiển thị nhân viên thử việc có vị trí được chọn.

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: F2_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.
  2. Chọn một phòng ban từ bộ lọc phòng ban.
  3. Nhấn Áp dụng.

Kết quả mong muốn:
  Danh sách hiển thị nhân viên thử việc thuộc phòng ban được chọn.

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: F2_004
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân viên thử việc: Tên nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị tên nhân viên thử việc trong cột 'Tên nhân viên'.

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: F2_005
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân viên thử việc: Phòng ban
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị phòng ban của nhân viên thử việc trong cột 'Phòng ban'.

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: F2_006
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân viên thử việc: Vị trí
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị vị trí của nhân viên thử việc trong cột 'Vị trí'.

--------------------------------------------------------------------------------

Test Case #23
----------------------------------------
ID: F2_007
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân viên thử việc: Hình thức
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị hình thức làm việc của nhân viên thử việc trong cột 'Hình thức'.

--------------------------------------------------------------------------------

Test Case #24
----------------------------------------
ID: F2_008
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân viên thử việc: Ngày bắt đầu
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị ngày bắt đầu thử việc của nhân viên trong cột 'Ngày bắt đầu'.

--------------------------------------------------------------------------------

Test Case #25
----------------------------------------
ID: F2_009
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân viên thử việc: Ngày kết thúc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.

Kết quả mong muốn:
  Hiển thị ngày kết thúc thử việc của nhân viên trong cột 'Ngày kết thúc'.

--------------------------------------------------------------------------------

Test Case #26
----------------------------------------
ID: F2_010
Mục đích kiểm thử: Kiểm tra điều hướng đến màn hình chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.
  2. Chọn một nhân viên thử việc.
  3. Nhấn button 'Chỉnh sửa' tương ứng với nhân viên đó.

Kết quả mong muốn:
  Điều hướng đến màn hình chỉnh sửa thông tin thử việc của nhân viên được chọn.

--------------------------------------------------------------------------------

Test Case #27
----------------------------------------
ID: F2_011
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.
  2. Click vào tên nhân viên đã có bản ghi thử việc.

Kết quả mong muốn:
  Chuyển đến màn Thông tin thử việc của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #28
----------------------------------------
ID: F2_012
Mục đích kiểm thử: Kiểm tra click vào tên nhân viên chưa có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.
  2. Click vào tên nhân viên chưa có bản ghi thử việc.

Kết quả mong muốn:
  Hiển thị popup 'Bạn có muốn tạo Thông tin Thử việc'.

--------------------------------------------------------------------------------

Test Case #29
----------------------------------------
ID: F2_013
Mục đích kiểm thử: Kiểm tra click button 'Có' trong popup tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.
  2. Click vào tên nhân viên chưa có bản ghi thử việc.
  3. Click button 'Có' trong popup.

Kết quả mong muốn:
  Chuyển đến màn hình tạo Thông tin thử việc của nhân viên đó.

--------------------------------------------------------------------------------

Test Case #30
----------------------------------------
ID: F2_014
Mục đích kiểm thử: Kiểm tra click button 'Hủy' trong popup tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình danh sách thử việc.
  2. Click vào tên nhân viên chưa có bản ghi thử việc.
  3. Click button 'Hủy' trong popup.

Kết quả mong muốn:
  Đóng popup và không chuyển đến màn hình nào.

--------------------------------------------------------------------------------

Test Case #31
----------------------------------------
ID: F2_015
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu màn hình tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo thông tin thử việc.

Kết quả mong muốn:
  Các button đều enable trừ button Lưu/Lưu cập nhật disable.

--------------------------------------------------------------------------------

Test Case #32
----------------------------------------
ID: F2_016
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu màn hình chỉnh sửa thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình chỉnh sửa thông tin thử việc.

Kết quả mong muốn:
  Các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable.

--------------------------------------------------------------------------------

Test Case #33
----------------------------------------
ID: F2_017
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, hình thức làm việc.

--------------------------------------------------------------------------------

Test Case #34
----------------------------------------
ID: F2_018
Mục đích kiểm thử: Kiểm tra chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Chọn một quản lý trực tiếp từ ô 'Chọn quản lý trực tiếp'.

Kết quả mong muốn:
  Quản lý trực tiếp được chọn hiển thị trong ô 'Chọn quản lý trực tiếp'. Chỉ được phép chọn 1 quản lý.

--------------------------------------------------------------------------------

Test Case #35
----------------------------------------
ID: F2_019
Mục đích kiểm thử: Kiểm tra chọn người theo dõi
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Chọn một hoặc nhiều người theo dõi từ ô 'Chọn người theo dõi'.

Kết quả mong muốn:
  Người theo dõi được chọn hiển thị trong ô 'Chọn người theo dõi'. Được phép chọn nhiều người.

--------------------------------------------------------------------------------

Test Case #36
----------------------------------------
ID: F2_020
Mục đích kiểm thử: Kiểm tra nhập ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Nhập ngày bắt đầu thử việc vào ô 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu thử việc được nhập hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #37
----------------------------------------
ID: F2_021
Mục đích kiểm thử: Kiểm tra nhập ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Nhập ngày kết thúc thử việc vào ô 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc thử việc được nhập hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #38
----------------------------------------
ID: F2_022
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc (tự động)
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Nhập ngày kết thúc thử việc vào ô 'Ngày kết thúc thử việc'.

Kết quả mong muốn:
  Ngày kết thúc được nhập hiển thị ở mục 'Ngày kết thúc' (hiển thị).

--------------------------------------------------------------------------------

Test Case #39
----------------------------------------
ID: F2_023
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu (tự động)
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Nhập ngày bắt đầu thử việc vào ô 'Ngày bắt đầu thử việc'.

Kết quả mong muốn:
  Ngày bắt đầu được nhập hiển thị ở mục 'Ngày bắt đầu' (hiển thị).

--------------------------------------------------------------------------------

Test Case #40
----------------------------------------
ID: F2_024
Mục đích kiểm thử: Kiểm tra thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Nhập tên giai đoạn vào ô 'Tên giai đoạn'.
  3. Nhấn button 'Thêm giai đoạn' (+).

Kết quả mong muốn:
  Giai đoạn mới được thêm và hiển thị ở phía dưới.

--------------------------------------------------------------------------------

Test Case #41
----------------------------------------
ID: F2_025
Mục đích kiểm thử: Kiểm tra xóa giai đoạn thử việc (chưa có kết quả đánh giá/phê duyệt)
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Thêm một giai đoạn thử việc mới.
  3. Nhấn button 'Xóa giai đoạn' tương ứng với giai đoạn đó.

Kết quả mong muốn:
  Giai đoạn bị xóa khỏi danh sách giai đoạn.

--------------------------------------------------------------------------------

Test Case #42
----------------------------------------
ID: F2_026
Mục đích kiểm thử: Kiểm tra không hiển thị button xóa giai đoạn đã có kết quả đánh giá/phê duyệt
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập màn hình chỉnh sửa thông tin thử việc.
  2. Kiểm tra giai đoạn đã có kết quả đánh giá/phê duyệt.

Kết quả mong muốn:
  Button 'Xóa giai đoạn' không hiển thị.

--------------------------------------------------------------------------------

Test Case #43
----------------------------------------
ID: F2_027
Mục đích kiểm thử: Kiểm tra chọn người đánh giá cho giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Thêm một giai đoạn thử việc mới.
  3. Chọn một hoặc nhiều người đánh giá từ ô 'Chọn người đánh giá' trong giai đoạn đó.

Kết quả mong muốn:
  Người đánh giá được chọn hiển thị trong ô 'Chọn người đánh giá'. Được phép chọn nhiều người.

--------------------------------------------------------------------------------

Test Case #44
----------------------------------------
ID: F2_028
Mục đích kiểm thử: Kiểm tra chọn mẫu đánh giá cho người đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Thêm một giai đoạn thử việc mới.
  3. Chọn một người đánh giá.
  4. Chọn một mẫu đánh giá từ ô 'Chọn mẫu đánh giá' tương ứng với người đánh giá đó.

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô 'Chọn mẫu đánh giá'.

--------------------------------------------------------------------------------

Test Case #45
----------------------------------------
ID: F2_029
Mục đích kiểm thử: Kiểm tra hiển thị form cài đặt trọng số chấm điểm
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Thêm một giai đoạn thử việc mới.
  3. Chọn một người đánh giá và một mẫu đánh giá.
  4. Ấn button 'Cài đặt trọng số chấm điểm'.

Kết quả mong muốn:
  Hiển thị form 'Cài đặt trọng số chấm điểm'.

--------------------------------------------------------------------------------

Test Case #46
----------------------------------------
ID: F2_030
Mục đích kiểm thử: Kiểm tra input nhập trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Mở form 'Cài đặt trọng số chấm điểm'.
  3. Nhập trọng số cho các tiêu chí.

Kết quả mong muốn:
  Trọng số được nhập hiển thị chính xác trong ô input.

--------------------------------------------------------------------------------

Test Case #47
----------------------------------------
ID: F2_031
Mục đích kiểm thử: Kiểm tra nhập hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Thêm một giai đoạn thử việc mới.
  3. Nhập hạn đánh giá vào ô 'Hạn đánh giá'.

Kết quả mong muốn:
  Hạn đánh giá được nhập hiển thị chính xác.

--------------------------------------------------------------------------------

Test Case #48
----------------------------------------
ID: F2_032
Mục đích kiểm thử: Kiểm tra nhập hạn phê duyệt
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập màn hình tạo/chỉnh sửa thông tin thử việc.
  2. Thêm một giai đoạn thử việc mới.
  3. Nh

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 48
Generated by Test Case Generator v1.0
