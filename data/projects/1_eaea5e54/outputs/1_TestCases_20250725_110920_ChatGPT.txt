================================================================================
TEST CASES - 1
Generated on: 2025-07-25 11:09:20
================================================================================


============================================================
KIỂM TRA GIAO DIỆN NGƯỜI DÙNG
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra màn hình Popup Tạo mới chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở màn hình Popup Tạo mới chức danh
  2. Nhập đủ thông tin
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo thành công sau khi thêm chức danh vào hệ thống

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra màn hình Popup Sửa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn chức danh từ danh sách
  2. Nhấn nút Sửa
  3. Chỉnh sửa thông tin
  4. Nhấn Lưu

Kết quả mong muốn:
  Dữ liệu được cập nhật thành công sau khi chỉnh sửa chức danh

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_003
Mục đích kiểm thử: Kiểm tra màn hình Popup Xóa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn chức danh từ danh sách
  2. Nhấn Xóa
  3. Xác nhận xóa
  4. Nhấn Đồng ý

Kết quả mong muốn:
  Hiển thị thông báo xác nhận xóa thành công sau khi xóa chức danh

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra giao diện ô nhập Tìm kiếm chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập từ khóa vào ô tìm kiếm
  2. Quan sát hệ thống filter real-time

Kết quả mong muốn:
  Danh sách chức danh được lọc theo từ khóa tìm kiếm mà không phân biệt hoa thường

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE DỮ LIỆU ĐẦU VÀO
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra nhập thiếu thông tin khi tạo mới chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở màn hình Popup Tạo mới chức danh
  2. Bỏ trống trường Tên chức danh
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập Tên chức danh'

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra nhập quá ký tự cho trường Tên chức danh
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở màn hình Popup Tạo mới chức danh
  2. Nhập quá 64 ký tự cho trường Tên chức danh
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên chức danh không được quá 64 ký tự'

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh với từ khóa tồn tại
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa đã tồn tại vào ô tìm kiếm
  2. Quan sát hệ thống filter real-time

Kết quả mong muốn:
  Danh sách chức danh hiển thị đúng với từ khóa tìm kiếm

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_SEARCH_002
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh với từ khóa không tồn tại
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập từ khóa không tồn tại vào ô tìm kiếm
  2. Quan sát hệ thống filter real-time

Kết quả mong muốn:
  Không có chức danh nào hiển thị sau khi tìm kiếm với từ khóa không tồn tại

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra thêm mới chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở màn hình Popup Tạo mới chức danh
  2. Nhập đủ thông tin
  3. Nhấn Lưu

Kết quả mong muốn:
  Chức danh mới được thêm vào hệ thống thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra sửa chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn chức danh từ danh sách
  2. Nhấn nút Sửa
  3. Chỉnh sửa thông tin
  4. Nhấn Lưu

Kết quả mong muốn:
  Chức danh được cập nhật thành công sau khi chỉnh sửa

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Kiểm tra xóa chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn chức danh từ danh sách
  2. Nhấn Xóa
  3. Xác nhận xóa
  4. Nhấn Đồng ý

Kết quả mong muốn:
  Chức danh được xóa khỏi hệ thống thành công

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_FUN_004
Mục đích kiểm thử: Kiểm tra không cho phép xóa chức danh đang được gán cho nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn chức danh đang được gán cho nhân viên
  2. Nhấn Xóa
  3. Quan sát thông báo

Kết quả mong muốn:
  Hiển thị thông báo không cho phép xóa chức danh đang được gán cho nhân viên

--------------------------------------------------------------------------------



============================================================
KIỂM TRA XỬ LÝ LỖI
============================================================

Test Case #1
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi trường Tên chức danh bị bỏ trống
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở màn hình Popup Tạo mới chức danh
  2. Bỏ trống trường Tên chức danh
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Vui lòng nhập Tên chức danh'

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_ERR_002
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi trùng tên chức danh khi sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn chức danh từ danh sách
  2. Nhấn nút Sửa
  3. Sửa Tên chức danh thành trùng với chức danh khác
  4. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi 'Tên chức danh đã tồn tại'

--------------------------------------------------------------------------------



============================================================
KIỂM TRA HIỆU NĂNG
============================================================

Test Case #1
----------------------------------------
ID: TC_PERF_001
Mục đích kiểm thử: Kiểm tra hiệu năng khi tìm kiếm chức danh với danh sách lớn
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở trang tìm kiếm chức danh
  2. Nhập từ khóa vào ô tìm kiếm
  3. Quan sát thời gian hiển thị kết quả

Kết quả mong muốn:
  Hệ thống hiển thị kết quả tìm kiếm trong thời gian chấp nhận được

--------------------------------------------------------------------------------


Total Test Cases Generated: 15
Generated by Test Case Generator v1.0
