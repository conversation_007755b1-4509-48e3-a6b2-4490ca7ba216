================================================================================
TEST CASES - ABC
Generated on: 2025-07-30 10:40:49
================================================================================


============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI THÔNG TIN THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị màn danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở màn hình danh sách thử việc
  2. Kiểm tra hiển thị bộ lọc, tên nhân viên, phòng ban, vị trí, hình thức, ngày bắt đầu, ngày kết thúc, button chỉnh sửa
  3. Xác nhận các thông tin hiển thị chính xác

Kết quả mong muốn:
  Màn hình danh sách thử việc hiển thị đúng thông tin nhân viên và các trường thông tin khác

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra chức năng tạo thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở màn hình tạo thông tin thử việc
  2. Nhập đầy đủ thông tin nhân viên, phòng ban, vị trí, hình thức, ngày bắt đầu, ngày kết thúc
  3. Nhấn button Lưu
  4. Kiểm tra thông báo thành công

Kết quả mong muốn:
  Thông báo Tạo thông tin thử việc thành công hiển thị sau khi lưu thông tin thành công

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_003
Mục đích kiểm thử: Kiểm tra chức năng chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở màn hình chỉnh sửa thông tin thử việc
  2. Thay đổi thông tin nhân viên, phòng ban, vị trí, hình thức, ngày bắt đầu, ngày kết thúc
  3. Nhấn button Lưu cập nhật
  4. Kiểm tra thông báo thành công

Kết quả mong muốn:
  Thông báo Cập nhật thông tin thử việc thành công hiển thị sau khi cập nhật thông tin thành công

--------------------------------------------------------------------------------



============================================================
KIỂM TRA MÔ HÌNH DỮ LIỆU
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra dữ liệu nhập cho tên nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập tên nhân viên
  2. Kiểm tra độ dài tên nhân viên
  3. Kiểm tra ký tự đặc biệt
  4. Kiểm tra việc lưu trữ dữ liệu

Kết quả mong muốn:
  Dữ liệu tên nhân viên được lưu trữ đúng định dạng và độ dài

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra dữ liệu nhập cho phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập phòng ban
  2. Kiểm tra độ dài phòng ban
  3. Kiểm tra ký tự đặc biệt
  4. Kiểm tra việc lưu trữ dữ liệu

Kết quả mong muốn:
  Dữ liệu phòng ban được lưu trữ đúng định dạng và độ dài

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra dữ liệu nhập cho ngày bắt đầu thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn ngày bắt đầu
  2. Kiểm tra định dạng ngày tháng
  3. Kiểm tra ngày bắt đầu không sau ngày kết thúc
  4. Kiểm tra việc lưu trữ dữ liệu

Kết quả mong muốn:
  Dữ liệu ngày bắt đầu thử việc được lưu trữ đúng định dạng và logic

--------------------------------------------------------------------------------



============================================================
KIỂM TRA MÔ TẢ LOGIC
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra chức năng lọc theo vị trí
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bộ lọc theo vị trí
  2. Xác nhận hiển thị danh sách nhân viên theo vị trí đã chọn

Kết quả mong muốn:
  Danh sách nhân viên được lọc chính xác theo vị trí đã chọn

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra chức năng hiển thị tên nhân viên thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào tên nhân viên
  2. Xác nhận chuyển đến màn hình thông tin thử việc hoặc hiển thị popup tạo thông tin thử việc

Kết quả mong muốn:
  Chuyển đến màn hình thông tin thử việc hoặc hiển thị popup tạo thông tin thử việc đúng

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Kiểm tra chức năng hiển thị phòng ban của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Xác nhận hiển thị phòng ban của nhân viên thử việc

Kết quả mong muốn:
  Phòng ban của nhân viên thử việc được hiển thị chính xác

--------------------------------------------------------------------------------



============================================================
KIỂM TRA MÔ TẢ LUỒNG XỬ LÝ
============================================================

Test Case #1
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu khi loading lần đầu tiên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở màn hình thử việc
  2. Kiểm tra trạng thái ban đầu của các button
  3. Xác nhận trạng thái ban đầu của các button

Kết quả mong muốn:
  Các button được hiển thị ở trạng thái enable trừ button Lưu/Lưu cập nhật

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_ERR_002
Mục đích kiểm thử: Kiểm tra chức năng thêm giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào button Thêm giai đoạn
  2. Xác nhận giai đoạn mới được thêm vào danh sách

Kết quả mong muốn:
  Giai đoạn mới được thêm vào danh sách thành công

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_ERR_003
Mục đích kiểm thử: Kiểm tra chức năng chọn kết quả mặc định
Độ ưu tiên: Low

Các bước thực hiện:
  1. Chọn kết quả mặc định
  2. Xác nhận kết quả được chọn

Kết quả mong muốn:
  Kết quả mặc định được chọn thành công

--------------------------------------------------------------------------------


Total Test Cases Generated: 12
Generated by Test Case Generator v1.0
