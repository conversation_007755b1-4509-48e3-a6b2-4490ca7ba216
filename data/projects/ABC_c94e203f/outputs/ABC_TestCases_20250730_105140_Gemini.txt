================================================================================
TEST CASES - ABC
Generated on: 2025-07-30 10:51:40
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống
  2. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Màn hình hiển thị danh sách thử việc với các cột: Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Các button đều enable trừ button Lưu/Lưu cập nhật disable

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo tên nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập tên nhân viên vào ô input tìm kiếm
  2. Nhấn Enter hoặc click button tìm kiếm

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên có tên trùng khớp với từ khóa tìm kiếm

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_SEARCH_002
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn phòng ban từ dropdown bộ lọc
  2. Nhấn Enter hoặc click button tìm kiếm

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên thuộc phòng ban đã chọn

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_SEARCH_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn vị trí từ dropdown bộ lọc
  2. Nhấn Enter hoặc click button tìm kiếm

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên có vị trí đã chọn

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra hiển thị tên nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Tên nhân viên được hiển thị chính xác

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra hiển thị phòng ban của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Phòng ban của nhân viên được hiển thị chính xác

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Kiểm tra hiển thị vị trí của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Vị trí của nhân viên được hiển thị chính xác

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_004
Mục đích kiểm thử: Kiểm tra hiển thị hình thức làm việc của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Hình thức làm việc của nhân viên được hiển thị chính xác

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_005
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu thử việc của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Ngày bắt đầu thử việc của nhân viên được hiển thị chính xác

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_FUN_006
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc thử việc của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc

Kết quả mong muốn:
  Ngày kết thúc thử việc của nhân viên được hiển thị chính xác

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_FUN_007
Mục đích kiểm thử: Kiểm tra điều hướng đến màn chỉnh sửa thông tin thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc
  2. Click button Chỉnh sửa của một nhân viên

Kết quả mong muốn:
  Điều hướng đến màn chỉnh sửa thông tin thử việc của nhân viên đó

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_FUN_008
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc
  2. Click vào tên nhân viên đã có bản ghi thử việc

Kết quả mong muốn:
  Chuyển đến màn Thông tin thử việc của nhân viên đó

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: TC_FUN_009
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên chưa có bản ghi thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc
  2. Click vào tên nhân viên chưa có bản ghi thử việc

Kết quả mong muốn:
  Hiện popup \Bạn có muốn tạo Thông tin Thử việc?\

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: TC_FUN_010
Mục đích kiểm thử: Kiểm tra click button Có trên popup tạo thông tin thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc
  2. Click vào tên nhân viên chưa có bản ghi thử việc
  3. Click button Có trên popup

Kết quả mong muốn:
  Chuyển đến màn tạo Thông tin thử việc của nhân viên đó

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: TC_FUN_011
Mục đích kiểm thử: Kiểm tra click button Hủy trên popup tạo thông tin thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình danh sách thử việc
  2. Click vào tên nhân viên chưa có bản ghi thử việc
  3. Click button Hủy trên popup

Kết quả mong muốn:
  Đóng popup

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO/CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_012
Mục đích kiểm thử: Kiểm tra hiển thị thông tin nhân sự thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc

Kết quả mong muốn:
  Hiển thị thông tin của nhân sự thử việc bao gồm: tên nhân sự, email, phòng ban, vị trí, hình thức làm việc

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_013
Mục đích kiểm thử: Kiểm tra hiển thị các ô input
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc

Kết quả mong muốn:
  Hiển thị các ô input: Chọn quản lý trực tiếp, Chọn người theo dõi, Ngày bắt đầu thử việc, Ngày kết thúc thử việc

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_014
Mục đích kiểm thử: Kiểm tra hiển thị các button
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc

Kết quả mong muốn:
  Hiển thị các button: Thêm giai đoạn, Xóa giai đoạn, Lưu, Hủy

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation trường Chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Để trống trường Chọn quản lý trực tiếp
  3. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Vui lòng chọn quản lý trực tiếp\

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation trường Ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Để trống trường Ngày bắt đầu thử việc
  3. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Vui lòng nhập ngày bắt đầu thử việc\

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra validation trường Ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Để trống trường Ngày kết thúc thử việc
  3. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Vui lòng nhập ngày kết thúc thử việc\

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_015
Mục đích kiểm thử: Kiểm tra chức năng chọn quản lý trực tiếp
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Chọn quản lý trực tiếp
  3. Chọn một quản lý từ danh sách

Kết quả mong muốn:
  Quản lý được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_016
Mục đích kiểm thử: Kiểm tra chức năng chọn nhiều người theo dõi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Chọn người theo dõi
  3. Chọn nhiều người theo dõi từ danh sách

Kết quả mong muốn:
  Những người theo dõi được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_017
Mục đích kiểm thử: Kiểm tra chức năng nhập ngày bắt đầu thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Ngày bắt đầu thử việc
  3. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_018
Mục đích kiểm thử: Kiểm tra chức năng nhập ngày kết thúc thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Ngày kết thúc thử việc
  3. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_FUN_019
Mục đích kiểm thử: Kiểm tra chức năng thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Nhập tên giai đoạn vào ô input
  3. Nhấn button Thêm giai đoạn

Kết quả mong muốn:
  Giai đoạn mới được hiển thị ở phía dưới

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_FUN_020
Mục đích kiểm thử: Kiểm tra chức năng xóa giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click button Xóa của một giai đoạn

Kết quả mong muốn:
  Giai đoạn đó bị xóa khỏi danh sách

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_FUN_021
Mục đích kiểm thử: Kiểm tra chức năng chọn người đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Chọn người đánh giá trong một giai đoạn
  3. Chọn một người đánh giá từ danh sách

Kết quả mong muốn:
  Người đánh giá được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: TC_FUN_022
Mục đích kiểm thử: Kiểm tra chức năng chọn mẫu đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Chọn mẫu đánh giá cho một người đánh giá
  3. Chọn một mẫu đánh giá từ danh sách

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: TC_FUN_023
Mục đích kiểm thử: Kiểm tra chức năng cài đặt trọng số chấm điểm
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click button Cài đặt trọng số chấm điểm

Kết quả mong muốn:
  Hiển thị form cài đặt trọng số chấm điểm

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: TC_FUN_024
Mục đích kiểm thử: Kiểm tra chức năng nhập trọng số
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click button Cài đặt trọng số chấm điểm
  3. Nhập trọng số cho các tiêu chí

Kết quả mong muốn:
  Trọng số được nhập hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: TC_FUN_025
Mục đích kiểm thử: Kiểm tra chức năng nhập hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Hạn đánh giá giai đoạn
  3. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: TC_FUN_026
Mục đích kiểm thử: Kiểm tra chức năng nhập hạn phê duyệt
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Hạn phê duyệt giai đoạn
  3. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: TC_FUN_027
Mục đích kiểm thử: Kiểm tra chức năng chọn kết quả mặc định
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input Chọn kết quả mặc định phê duyệt cho giai đoạn
  3. Chọn một kết quả từ danh sách

Kết quả mong muốn:
  Kết quả được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: TC_FUN_028
Mục đích kiểm thử: Kiểm tra chức năng chọn số ngày gửi thông báo nhắc nhở đánh giá trước
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click vào ô input chọn số ngày gửi thông báo nhắc nhở đánh giá trước
  3. Chọn một số ngày từ danh sách

Kết quả mong muốn:
  Số ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: TC_FUN_029
Mục đích kiểm thử: Kiểm tra chức năng Lưu thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Nhập đầy đủ thông tin hợp lệ
  3. Nhấn button Lưu

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và chuyển về màn hình danh sách thử việc

--------------------------------------------------------------------------------

Test Case #22
----------------------------------------
ID: TC_FUN_030
Mục đích kiểm thử: Kiểm tra chức năng Hủy tạo thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Nhấn button Hủy

Kết quả mong muốn:
  Hủy tạo thử việc và quay trở lại màn hình list thử việc

--------------------------------------------------------------------------------

Test Case #23
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Kiểm tra thông báo lỗi khi nhập sai định dạng ngày
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Nhập sai định dạng ngày vào ô Ngày bắt đầu thử việc
  3. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Định dạng ngày không hợp lệ\

--------------------------------------------------------------------------------

Test Case #24
----------------------------------------
ID: TC_ERR_002
Mục đích kiểm thử: Kiểm tra thông báo lỗi khi nhập trọng số không phải số nguyên dương
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình tạo/chỉnh sửa thử việc
  2. Click button Cài đặt trọng số chấm điểm
  3. Nhập trọng số không phải số nguyên dương vào ô input trọng số
  4. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Trọng số phải là số nguyên dương\

--------------------------------------------------------------------------------

Test Case #25
----------------------------------------
ID: TC_UI_031
Mục đích kiểm thử: Kiểm tra trạng thái disable của các trường thông tin giai đoạn đã có kết quả
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình chỉnh sửa thử việc của nhân viên đã có kết quả đánh giá/phê duyệt giai đoạn

Kết quả mong muốn:
  Các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable

--------------------------------------------------------------------------------

Test Case #26
----------------------------------------
ID: TC_FUN_032
Mục đích kiểm thử: Kiểm tra button Xóa giai đoạn chỉ hiện ở những giai đoạn chưa có kết quả đánh giá/phê duyệt
Độ ưu tiên: High

Các bước thực hiện:
  1. Điều hướng đến màn hình chỉnh sửa thử việc
  2. Kiểm tra button Xóa giai đoạn

Kết quả mong muốn:
  Button Xóa giai đoạn chỉ hiện ở những giai đoạn chưa có kết quả đánh giá/phê duyệt

--------------------------------------------------------------------------------


Total Test Cases Generated: 42
Generated by Test Case Generator v1.0
