{"eaea5e54-6036-43fa-93dc-a2bb04f2a229": {"id": "eaea5e54-6036-43fa-93dc-a2bb04f2a229", "name": "1", "description": "1", "created_at": "2025-07-25T11:08:13.690281", "updated_at": "2025-07-29T16:53:12.489816", "files": [{"id": "a3d44566-cb2c-44fd-83ad-730d37a3e8d6", "path": "data/projects/1_eaea5e54/inputs/def632dd-6786-49cc-9f50-9b8d5c56ef78_input-urd-sample.docx", "original_name": "input-urd-sample.docx", "file_type": ".docx", "file_size": 7659, "uploaded_at": "2025-07-25T11:08:43.369603", "processing_status": "processed", "category": "input", "updated_at": "2025-07-29T16:53:12.306933"}, {"id": "bb267974-973b-455f-92c4-4b04d1060972", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_110920_ChatGPT.txt", "relative_path": "outputs/1_TestCases_20250725_110920_ChatGPT.txt", "original_name": "1_TestCases_20250725_110920_ChatGPT.txt", "file_type": ".txt", "file_size": 8834, "uploaded_at": "2025-07-25T11:09:20.919616", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.308978"}, {"id": "0651211b-0e01-4fac-bd21-1348661f1591", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_110920_ChatGPT.xlsx", "relative_path": "outputs/1_TestCases_20250725_110920_ChatGPT.xlsx", "original_name": "1_TestCases_20250725_110920_ChatGPT.xlsx", "file_type": ".xlsx", "file_size": 7341, "uploaded_at": "2025-07-25T11:09:20.919630", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.310221"}, {"id": "da3f780e-c31d-479c-8818-0bbf8899e9a4", "path": "data/projects/1_eaea5e54/inputs/dfb78315-702b-499c-90f1-87800ff8a1d4_tạo mới thông tin thử việc.docx", "original_name": "tạo mới thông tin thử việc.docx", "file_type": ".docx", "file_size": 27772, "uploaded_at": "2025-07-25T11:12:10.444564", "processing_status": "processed", "category": "input", "updated_at": "2025-07-29T16:53:12.311303"}, {"id": "77ec9c18-8d92-4e6e-ba4d-2f2816b078da", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_152213_ChatGPT.txt", "relative_path": "outputs/1_TestCases_20250725_152213_ChatGPT.txt", "original_name": "1_TestCases_20250725_152213_ChatGPT.txt", "file_type": ".txt", "file_size": 9119, "uploaded_at": "2025-07-25T15:22:13.848803", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.312320"}, {"id": "02871f17-ba0e-4a2d-8df7-2602c5d860a5", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_152213_ChatGPT.xlsx", "relative_path": "outputs/1_TestCases_20250725_152213_ChatGPT.xlsx", "original_name": "1_TestCases_20250725_152213_ChatGPT.xlsx", "file_type": ".xlsx", "file_size": 7448, "uploaded_at": "2025-07-25T15:22:13.848827", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.313235"}, {"id": "4522cf90-7957-425f-b6ab-cf109545790b", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_155954_Gemini.txt", "relative_path": "outputs/1_TestCases_20250725_155954_Gemini.txt", "original_name": "1_TestCases_20250725_155954_Gemini.txt", "file_type": ".txt", "file_size": 28318, "uploaded_at": "2025-07-25T15:59:54.445701", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.314149"}, {"id": "d2eb349b-223a-4d37-870d-f7e9af42ab04", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_155954_Gemini.xlsx", "relative_path": "outputs/1_TestCases_20250725_155954_Gemini.xlsx", "original_name": "1_TestCases_20250725_155954_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9956, "uploaded_at": "2025-07-25T15:59:54.445755", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.315060"}, {"id": "005fffeb-5907-4a78-8f56-6378a3db2070", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_161048_Gemini.txt", "relative_path": "outputs/1_TestCases_20250725_161048_Gemini.txt", "original_name": "1_TestCases_20250725_161048_Gemini.txt", "file_type": ".txt", "file_size": 23976, "uploaded_at": "2025-07-25T16:10:49.107659", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.316147"}, {"id": "3bb3daf2-8410-4fda-8157-c3be5da3b1f4", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_161048_Gemini.xlsx", "relative_path": "outputs/1_TestCases_20250725_161048_Gemini.xlsx", "original_name": "1_TestCases_20250725_161048_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9621, "uploaded_at": "2025-07-25T16:10:49.107685", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.317168"}, {"id": "9004c706-4c3e-4d39-bc57-81f0b3422b5e", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_113820_ChatGPT.txt", "relative_path": "outputs/1_TestCases_20250729_113820_ChatGPT.txt", "original_name": "1_TestCases_20250729_113820_ChatGPT.txt", "file_type": ".txt", "file_size": 4475, "uploaded_at": "2025-07-29T11:38:20.630265", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.318195"}, {"id": "ef38aa40-52d4-411d-9994-3d489c3998a4", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_113820_ChatGPT.xlsx", "relative_path": "outputs/1_TestCases_20250729_113820_ChatGPT.xlsx", "original_name": "1_TestCases_20250729_113820_ChatGPT.xlsx", "file_type": ".xlsx", "file_size": 6614, "uploaded_at": "2025-07-29T11:38:20.630339", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.319005"}, {"id": "ff580fc9-b757-4468-bf79-57564928be11", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120043_ChatGPT.txt", "relative_path": "outputs/1_TestCases_20250729_120043_ChatGPT.txt", "original_name": "1_TestCases_20250729_120043_ChatGPT.txt", "file_type": ".txt", "file_size": 754, "uploaded_at": "2025-07-29T12:00:43.662475", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.319784"}, {"id": "00a70670-da5b-412f-8a2a-a820bdd984bc", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120043_ChatGPT.xlsx", "relative_path": "outputs/1_TestCases_20250729_120043_ChatGPT.xlsx", "original_name": "1_TestCases_20250729_120043_ChatGPT.xlsx", "file_type": ".xlsx", "file_size": 6028, "uploaded_at": "2025-07-29T12:00:43.662502", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-29T16:53:12.320558"}, {"id": "4780f334-8488-4483-8a87-cab5480281e3", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120113_Gemini.txt", "relative_path": "outputs/1_TestCases_20250729_120113_Gemini.txt", "original_name": "1_TestCases_20250729_120113_Gemini.txt", "file_type": ".txt", "file_size": 28001, "uploaded_at": "2025-07-29T12:01:13.826538", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.321334"}, {"id": "f0c3dba4-891e-4440-b67a-58fca95cb345", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120113_Gemini.xlsx", "relative_path": "outputs/1_TestCases_20250729_120113_Gemini.xlsx", "original_name": "1_TestCases_20250729_120113_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9651, "uploaded_at": "2025-07-29T12:01:13.826559", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.322194"}, {"id": "887f1728-34c2-4696-8ae5-f45e43331f1c", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_162944_Gemini.txt", "relative_path": "outputs/1_TestCases_20250729_162944_Gemini.txt", "original_name": "1_TestCases_20250729_162944_Gemini.txt", "file_type": ".txt", "file_size": 25735, "uploaded_at": "2025-07-29T16:29:44.458431", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.322907"}, {"id": "d7e5ce7c-ce9b-4774-a6fb-1927d289f377", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_162944_Gemini.xlsx", "relative_path": "outputs/1_TestCases_20250729_162944_Gemini.xlsx", "original_name": "1_TestCases_20250729_162944_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9606, "uploaded_at": "2025-07-29T16:29:44.458454", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-29T16:53:12.323598"}, {"id": "ef72857e-f58e-439c-8ba8-7d62cbc41386", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_165312_Gemini.txt", "relative_path": "outputs/1_TestCases_20250729_165312_Gemini.txt", "original_name": "1_TestCases_20250729_165312_Gemini.txt", "file_type": ".txt", "file_size": 28193, "uploaded_at": "2025-07-29T16:53:12.489788", "processing_status": "processed", "category": "output", "model_used": "Gemini"}, {"id": "e0704db5-9016-45ce-b5b4-4a51fcbd281f", "path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_165312_Gemini.xlsx", "relative_path": "outputs/1_TestCases_20250729_165312_Gemini.xlsx", "original_name": "1_TestCases_20250729_165312_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9783, "uploaded_at": "2025-07-29T16:53:12.489806", "processing_status": "processed", "category": "output", "model_used": "Gemini"}], "generated_outputs": [{"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_110920_ChatGPT.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_110920_ChatGPT.xlsx", "generated_at": "2025-07-25T11:09:20.919633", "model_used": "ChatGPT"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_152213_ChatGPT.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_152213_ChatGPT.xlsx", "generated_at": "2025-07-25T15:22:13.848832", "model_used": "ChatGPT"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_155954_Gemini.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_155954_Gemini.xlsx", "generated_at": "2025-07-25T15:59:54.445762", "model_used": "Gemini"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_161048_Gemini.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250725_161048_Gemini.xlsx", "generated_at": "2025-07-25T16:10:49.107692", "model_used": "Gemini"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_113820_ChatGPT.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_113820_ChatGPT.xlsx", "generated_at": "2025-07-29T11:38:20.630357", "model_used": "ChatGPT"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120043_ChatGPT.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120043_ChatGPT.xlsx", "generated_at": "2025-07-29T12:00:43.662509", "model_used": "ChatGPT"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120113_Gemini.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_120113_Gemini.xlsx", "generated_at": "2025-07-29T12:01:13.826564", "model_used": "Gemini"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_162944_Gemini.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_162944_Gemini.xlsx", "generated_at": "2025-07-29T16:29:44.458462", "model_used": "Gemini"}, {"txt_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_165312_Gemini.txt", "excel_path": "data/projects/1_eaea5e54/outputs/1_TestCases_20250729_165312_Gemini.xlsx", "generated_at": "2025-07-29T16:53:12.489810", "model_used": "Gemini"}]}, "dc1200e6-720a-44e9-9c20-099254435ea9": {"id": "dc1200e6-720a-44e9-9c20-099254435ea9", "name": "Test", "description": "", "created_at": "2025-07-25T11:30:04.798688", "updated_at": "2025-07-25T11:32:32.837076", "files": [{"id": "f692cedf-f7cc-4ed7-bb93-b73220068d39", "path": "data/projects/Test_dc1200e6/inputs/005e09f9-227a-4637-b96f-7a29b14da67d_Qu<PERSON>n lý mẫu đánh giá.docx", "original_name": "<PERSON><PERSON><PERSON>n lý mẫu đánh giá.docx", "file_type": ".docx", "file_size": 22027, "uploaded_at": "2025-07-25T11:31:32.444754", "processing_status": "processed", "category": "input", "updated_at": "2025-07-25T11:32:32.804203"}, {"id": "74bd5605-b90f-410a-966d-53975257036c", "path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113205_Gemini.txt", "relative_path": "outputs/Test_TestCases_20250725_113205_Gemini.txt", "original_name": "Test_TestCases_20250725_113205_Gemini.txt", "file_type": ".txt", "file_size": 24248, "uploaded_at": "2025-07-25T11:32:05.547005", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-25T11:32:32.804643"}, {"id": "7eaff98d-4163-45cc-850d-454136d4edde", "path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113205_Gemini.xlsx", "relative_path": "outputs/Test_TestCases_20250725_113205_Gemini.xlsx", "original_name": "Test_TestCases_20250725_113205_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9342, "uploaded_at": "2025-07-25T11:32:05.547040", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-25T11:32:32.804962"}, {"id": "e5e1fd25-b1e2-4bf2-92ab-7e65b5110590", "path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113232_Gemini.txt", "relative_path": "outputs/Test_TestCases_20250725_113232_Gemini.txt", "original_name": "Test_TestCases_20250725_113232_Gemini.txt", "file_type": ".txt", "file_size": 25909, "uploaded_at": "2025-07-25T11:32:32.837035", "processing_status": "processed", "category": "output", "model_used": "Gemini"}, {"id": "f28a12fc-9f3d-4fcf-9bee-9226ab93f5b6", "path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113232_Gemini.xlsx", "relative_path": "outputs/Test_TestCases_20250725_113232_Gemini.xlsx", "original_name": "Test_TestCases_20250725_113232_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9150, "uploaded_at": "2025-07-25T11:32:32.837056", "processing_status": "processed", "category": "output", "model_used": "Gemini"}], "generated_outputs": [{"txt_path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113205_Gemini.txt", "excel_path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113205_Gemini.xlsx", "generated_at": "2025-07-25T11:32:05.547046", "model_used": "Gemini"}, {"txt_path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113232_Gemini.txt", "excel_path": "data/projects/Test_dc1200e6/outputs/Test_TestCases_20250725_113232_Gemini.xlsx", "generated_at": "2025-07-25T11:32:32.837072", "model_used": "Gemini"}]}, "e6b479c5-c65a-4fe0-8bd6-67c595808869": {"id": "e6b479c5-c65a-4fe0-8bd6-67c595808869", "name": "aaa", "description": "aaa", "created_at": "2025-07-25T11:41:18.044757", "updated_at": "2025-07-31T17:53:21.700672", "files": [{"id": "4c66c223-f432-49e7-b7d2-622a78ee17a0", "path": "data/projects/aaa_e6b479c5/inputs/497eaf51-0c14-4d1c-9a56-ad6f9f55287d_tạo mới thông tin thử việc.docx", "original_name": "tạo mới thông tin thử việc.docx", "file_type": ".docx", "file_size": 27772, "uploaded_at": "2025-07-31T17:04:17.230131", "processing_status": "processed", "category": "input", "metadata": {"raw_content": "M<PERSON>n danh sách thử việc\nMàn hình Tạo Thử việc:\n<PERSON><PERSON><PERSON> hình Chỉnh sửa Thử việc\nMô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | Nội dung | Nội dung\nTrạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable trừ button Lưu/Lưu cập nhật disable | các button đều enable trừ button Lưu/Lưu cập nhật disable | các button đều enable trừ button Lưu/Lưu cập nhật disable\n1 | B<PERSON> lọc | Bộ lọc | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm\n2 | Tên nhân viên | Tên nhân viên | Hiển thị Tên nhân viên | Hiển thị Tên nhân viên | Hiển thị Tên nhân viên\n3 | Phòng ban | Phòng ban | Hiển thị phòng ban của nhân viên | Hiển thị phòng ban của nhân viên | Hiển thị phòng ban của nhân viên\n4 | Vị trí | Vị trí | Hiển thị vị trí của nhân viên | Hiển thị vị trí của nhân viên | Hiển thị vị trí của nhân viên\n5 | Hình thức | Hình thức | Hiển thị hình thức làm việc của nhân viên | Hiển thị hình thức làm việc của nhân viên | Hiển thị hình thức làm việc của nhân viên\n6 | Ngày bắt đầu | Ngày bắt đầu | Hiển thị ngày bắt đầu thử việc của nhân viên | Hiển thị ngày bắt đầu thử việc của nhân viên | Hiển thị ngày bắt đầu thử việc của nhân viên\n7 | Ngày kết thúc | Ngày kết thúc | Hiển thị ngày kết thúc thử việc của nhân viên | Hiển thị ngày kết thúc thử việc của nhân viên | Hiển thị ngày kết thúc thử việc của nhân viên\n8 | Button Chỉnh sửa | Button Chỉnh sửa | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc\nMô hình dữ liệu\nSTT | Text hiển thị | Tên biến | Mã validate | Kiểu dữ liệu | Ý nghĩa | Lưu trữ\n1 | Bộ lọc\n2 | Tên nhân viên | name | INT | Người theo dõi tham chiếu tới bảng employee | wk_employee.employee.name\n3 | Phòng ban | title | VARCHAR(255) | Phòng ban của nhân sự thử việc | wk_employee.department.title\n4 | Vị trí | name | VARCHAR(255) | Vị trí làm việc của nhân sự thử việc | wk_employee.employment_position.name\n5 | Hình thức | name | VARCHAR(255) | Hình thức làm việc của nhân sự thử việc | wk_employee.employment_type.name\n6 | Ngày bắt đầu | start_date | DATE | Ngày bắt đầu thử việc | wk_probation.probation.start_date\n7 | Ngày kết thúc | end_date | DATE | Ngày kết thúc thử việc | wk_probation.probation.end_date\nMô tả logic\n1.1 | Tính năng\n1 | Bộ lọc | Lọc theo vị trí\nLọc theo phòng ban\n2 | Tên nhân viên | Hiển thị tên nhân viên thử việc\nKhi click tên nhân viên:\n+ Nếu nhân viên đã có bản ghi thử việc sẽ chuyển đến màn Thông tin thử việc. \n+Nếu không hiện popup \"Bạn có muốn tạo Thông tin Thử việc\" . Click Button Có => chuyển đến màn tạo Thông tin thử việc. Click btn Hủy => Đóng popup\n3 | Phòng ban | Hiển thị phòng ban của nhân viên thử việc\n4 | Vị trí | Hiển thị vị trí làm việc của nhân viên thử việc\n5 | Hình thức | Hiển thị hình thức làm việc của nhân viên thử việc\n6 | Ngày bắt đầu | Hiển thị ngày bắt đầu thử việc của nhân sự\n7 | Ngày kết thúc | Hiển thị ngày kết thúc thử việc của nhân sự\n8 | Button Chỉnh sửa | - Khi admin ấn button 1 thì chuyển đến màn Chỉnh sửa thử việc\nMô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | Nội dung | Nội dung\nTrạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable | các button đều enable | các button đều enable\n1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc\n2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp\n3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc\n6 | Ngày kết thúc | Ngày kết thúc | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5\n7 | Ngày bắt đầu | Ngày bắt đầu | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc\n9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc\n10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\n12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí\n14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn\n15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn\n16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn\n17 | Gửi thông báo nhắc nhở đánh giá trước | Gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước\n18 | Btn Lưu | Btn Lưu | Lưu thông tin thử việc | Lưu thông tin thử việc | Lưu thông tin thử việc\n19 | Btn Hủy | Btn Hủy | Hủy tạo thử việc | Hủy tạo thử việc | Hủy tạo thử việc\nMô hình dữ liệu\nSTT | Text hiển thị | Tên biến | validate | Kiểu dữ liệu | Ý nghĩa | Lưu trữ\n1 | Thông tin nhân sự thử việc\n2 | Chọn quản lý trực tiếp | direct_manager_id | - Required | INT | id quản lý trực tiếp tham chiếu tới bảng employee | wk_probation.probation.direct_manager_id\n3 | Chọn người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee | wk_probation.probation_followers.follower_id\n4 | Ngày bắt đầu thử việc | start_date | - Required | DATE | Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | end_date | - Required | DATE | Ngày kết thúc thử việc\n6 | Ngày kết thúc | end_date | DATE | Ngày kết thúc thử việc\n7 | Ngày bắt đầu | start_date | DATE | Ngày bắt đầu thử việc\n8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc | wk_probation.probation_stages.name\n9 | Btn Xóa giai đoạn\n10 | Chọn người đánh giá | evaluator_id | - Required | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee | wk_probation.evaluation_forms.evaluator_id\n11 | Chọn mẫu đánh giá | evaluation_template_id | - Required | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates | wk_probation.evaluation_forms.evaluation_template_id\n12 | Cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | weight_factor | - Required\n- Nhập nguyên dương | JSON | Trọng số cho các câu hỏi | wk_probation.evaluation_forms.weight_factor\n14 | Hạn đánh giá | evaluate_date | DATE | Hạn đánh giá giai đoạn | wk_probation.probation_stages.evaluate_date\n15 | Hạn phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn | wk_probation.probation_stages.approve_date\n16 | Chọn kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn | wk_probation.probation_stages.default_status\n17 | Gửi thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước | wk_probation.probation.remind_date\n18 | Btn Lưu\n19 | Btn Hủy\nMô tả logic\n1.1 | Tính năng\n1 | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, hình thức làm việc\n2 | Chọn quản lý trực tiếp | - Được phép chọn duy nhất 1 quản lý trực tiếp\n- Quản lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc\n- Quản lý trực tiếp sẽ là người phê duyệt giai đoạn\n3 | Chọn người theo dõi | - Được phép chọn nhiều người theo dõi\n- Người theo dõi sẽ có quyền xem chi tiết thử việc\n- Người theo dõi sẽ nhận được thông báo khi có người cmt vào thử việc\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu quá trình thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc quá trình thử việc\n6 | Ngày kết thúc | Hiển thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5\n7 | Ngày bắt đầu | Hiển thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới\n9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa\n10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn\nCó thể chọn nhiều người đánh giá cho cùng 1 giai đoạn\nNgười đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\nNgười đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá được chọn\n12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm\nSẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11\n13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi\ntrọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc\n14 | Hạn đánh giá | Ô input nhập hạn đánh giá\nHết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc\n15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn\nHết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc\nKhi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16\n16 | Chọn kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt\nNếu chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo\nNếu chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuyển nhân sự sang tab ending\n17 | Gửi thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá\nKhi trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá\n18 | Btn Lưu | Lưu thông tin thử việc\n19 | Btn Hủy | Hủy tạo mới thử việc , quay trở lại màn hình list thử việc\nMô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | Nội dung | Nội dung\nTrạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable\n1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc\n2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp\n3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc\n6 | Ngày kết thúc | Ngày kết thúc | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5\n7 | Ngày bắt đầu | Ngày bắt đầu | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc\n9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc\n10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\n12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí\n14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn\n15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn\n16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn\n17 | Gửi thông báo nhắc nhở đánh giá trước | Gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước\n18 | Btn Lưu | Btn Lưu | Lưu thông tin thử việc | Lưu thông tin thử việc | Lưu thông tin thử việc\n19 | Btn Hủy | Btn Hủy | Hủy tạo thử việc | Hủy tạo thử việc | Hủy tạo thử việc\nMô hình dữ liệu\nSTT | Text hiển thị | Tên biến | Mã validate | Kiểu dữ liệu | Ý nghĩa\n1 | Thông tin nhân sự thử việc\n2 | Chọn quản lý trực tiếp | direct_manager_id | INT | id quản lý trực tiếp tham chiếu tới bảng employee\n3 | Chọn người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee\n4 | Ngày bắt đầu thử việc | start_date | DATE | Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | end_date | DATE | Ngày kết thúc thử việc\n6 | Ngày kết thúc | end_date | DATE | Ngày kết thúc thử việc\n7 | Ngày bắt đầu | start_date | DATE | Ngày bắt đầu thử việc\n8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc\n9 | Btn Xóa giai đoạn\n10 | Chọn người đánh giá | evaluator_id | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee\n11 | Chọn mẫu đánh giá | evaluation_template_id | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates\n12 | Cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | weight_factor | JSON | Trọng số cho các câu hỏi\n14 | Hạn đánh giá | evaluate_date | DATE | Hạn đánh giá giai đoạn\n15 | Hạn phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn\n16 | Chọn kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn\n17 | Gửi thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước\n18 | Btn Lưu\n19 | Btn Hủy\nMô tả logic\n1.1 | Tính năng\n1 | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, hình thức làm việc\n2 | Chọn quản lý trực tiếp | - Được phép chọn duy nhất 1 quản lý trực tiếp\n- Quản lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc\n- Quản lý trực tiếp sẽ là người phê duyệt giai đoạn\n3 | Chọn người theo dõi | - Được phép chọn nhiều người theo dõi\n- Người theo dõi sẽ có quyền xem chi tiết thử việc\n- Người theo dõi sẽ nhận được thông báo khi có người cmt vào thử việc\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu quá trình thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc quá trình thử việc\n6 | Ngày kết thúc | Hiển thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5\n7 | Ngày bắt đầu | Hiển thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới\n9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa\nBtn Xóa giai đoạn chỉ hiện ở những giai đoạn nào chưa có kết quả đánh giá/phê duyệt\n10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn\nCó thể chọn nhiều người đánh giá cho cùng 1 giai đoạn\nNgười đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\nNgười đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá được chọn\n12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm\nSẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11\n13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi\ntrọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc\n14 | Hạn đánh giá | Ô input nhập hạn đánh giá\nHết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc\n15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn\nHết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc\nKhi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16\n16 | Chọn kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt\nNếu chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo\nNếu chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuyển nhân sự sang tab ending\n17 | Gửi thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá\nKhi trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá\n18 | Btn Lưu | Lưu thông tin thử việc\n19 | Btn Hủy | Hủy tạo mới thử việc , quay trở lại màn hình list thử việc", "functions": ["1.1 | <PERSON><PERSON><PERSON> n<PERSON>ng", "1 | <PERSON><PERSON> lọc | <PERSON>ọc theo vị trí", "<PERSON><PERSON><PERSON> theo phòng ban", "2 | T<PERSON><PERSON> nhân viên | <PERSON><PERSON><PERSON> thị tên nhân viên thử việc", "<PERSON>hi click tên nhân viên:", "+ <PERSON><PERSON><PERSON> nhân viên đã có bản ghi thử việc sẽ chuyển đến màn Thông tin thử việc.", "+<PERSON><PERSON><PERSON> không hiện popup \"Bạn có muốn tạo Thông tin Thử việc\" . Click Button Có => chuyển đến màn tạo Thông tin thử việc. Click btn Hủy => Đóng popup", "3 | <PERSON><PERSON>ng ban | <PERSON><PERSON><PERSON> thị phòng ban của nhân viên thử việc", "4 | <PERSON><PERSON> trí | <PERSON><PERSON><PERSON> thị vị trí làm việc của nhân viên thử việc", "5 | <PERSON><PERSON><PERSON> thức | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên thử việc", "6 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân sự", "7 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân sự", "8 | Button Chỉnh sửa | - <PERSON><PERSON> admin ấn button 1 thì chuyển đến màn Chỉnh sửa thử việc", "1.1 | <PERSON><PERSON><PERSON> n<PERSON>ng", "1 | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, h<PERSON><PERSON> thức làm việc", "2 | Chọn quản lý trực tiếp | - <PERSON><PERSON><PERSON><PERSON> phép chọn duy nhất 1 quản lý trực tiếp", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ là người phê duyệt giai đoạn", "3 | Ch<PERSON><PERSON> người theo dõi | - <PERSON><PERSON><PERSON><PERSON> phép chọn nhiều người theo dõi", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ có quyền xem chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ nhận đư<PERSON><PERSON> thông báo khi có người cmt vào thử việc", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu quá trình thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc quá trình thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới", "9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa", "10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn", "<PERSON><PERSON> thể chọn nhiều người đánh giá cho cùng 1 giai đoạn", "Người đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "Người đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá đượ<PERSON> chọn", "12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm", "Sẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11", "13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi", "trọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc", "14 | Hạn đánh giá | Ô input nhập hạn đánh giá", "Hết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc", "15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn", "<PERSON>ết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc", "<PERSON><PERSON> hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16", "16 | Ch<PERSON><PERSON> kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt", "<PERSON><PERSON><PERSON> chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo", "<PERSON><PERSON><PERSON> chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuy<PERSON>n nhân sự sang tab ending", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá", "<PERSON><PERSON> trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá", "18 | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | <PERSON><PERSON><PERSON> tạo mới thử việc , quay trở lại màn hình list thử việc", "1.1 | <PERSON><PERSON><PERSON> n<PERSON>ng", "1 | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, h<PERSON><PERSON> thức làm việc", "2 | Chọn quản lý trực tiếp | - <PERSON><PERSON><PERSON><PERSON> phép chọn duy nhất 1 quản lý trực tiếp", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ là người phê duyệt giai đoạn", "3 | Ch<PERSON><PERSON> người theo dõi | - <PERSON><PERSON><PERSON><PERSON> phép chọn nhiều người theo dõi", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ có quyền xem chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ nhận đư<PERSON><PERSON> thông báo khi có người cmt vào thử việc", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu quá trình thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc quá trình thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới", "9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa", "Btn Xóa giai đoạn chỉ hiện ở những giai đoạn nào chưa có kết quả đánh giá/phê duyệt", "10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn", "<PERSON><PERSON> thể chọn nhiều người đánh giá cho cùng 1 giai đoạn", "Người đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "Người đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá đượ<PERSON> chọn", "12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm", "Sẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11", "13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi", "trọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc", "14 | Hạn đánh giá | Ô input nhập hạn đánh giá", "Hết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc", "15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn", "<PERSON>ết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc", "<PERSON><PERSON> hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16", "16 | Ch<PERSON><PERSON> kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt", "<PERSON><PERSON><PERSON> chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo", "<PERSON><PERSON><PERSON> chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuy<PERSON>n nhân sự sang tab ending", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá", "<PERSON><PERSON> trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá", "18 | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | <PERSON><PERSON><PERSON> tạo mới thử việc , quay trở lại màn hình list thử việc"], "requirements": [], "validations": ["STT | Text hiển thị | Tê<PERSON> biến | Mã validate | Kiểu dữ liệu | Ý nghĩa | <PERSON><PERSON><PERSON> trữ", "1 | <PERSON><PERSON> lọc", "2 | Tê<PERSON> nhân viên | name | INT | Người theo dõi tham chiếu tới bảng employee | wk_employee.employee.name", "3 | Phòng ban | title | VARCHAR(255) | Phòng ban của nhân sự thử việc | wk_employee.department.title", "4 | V<PERSON> trí | name | VARCHAR(255) | Vị trí làm việc của nhân sự thử việc | wk_employee.employment_position.name", "5 | <PERSON><PERSON><PERSON> thức | name | VARCHAR(255) | <PERSON><PERSON><PERSON> thức làm việc của nhân sự thử việc | wk_employee.employment_type.name", "6 | <PERSON><PERSON><PERSON> bắt đầu | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc | wk_probation.probation.start_date", "7 | <PERSON><PERSON><PERSON> kết thúc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc | wk_probation.probation.end_date", "<PERSON><PERSON> tả logic", "STT | Text hiển thị | Tê<PERSON> biến | validate | <PERSON><PERSON><PERSON> dữ liệu | Ý nghĩa | <PERSON><PERSON><PERSON> trữ", "1 | Th<PERSON>ng tin nhân sự thử việc", "2 | Ch<PERSON><PERSON> quản lý trực tiếp | direct_manager_id | - Required | INT | id quản lý trực tiếp tham chi<PERSON>u tới bảng employee | wk_probation.probation.direct_manager_id", "3 | Ch<PERSON>n người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee | wk_probation.probation_followers.follower_id", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | start_date | - Required | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | end_date | - Required | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "7 | <PERSON><PERSON><PERSON> bắt đầu | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc | wk_probation.probation_stages.name", "9 | Btn Xóa giai đo<PERSON>n", "10 | Chọn người đánh giá | evaluator_id | - Required | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee | wk_probation.evaluation_forms.evaluator_id", "11 | Chọn mẫu đánh giá | evaluation_template_id | - Required | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates | wk_probation.evaluation_forms.evaluation_template_id", "12 | Cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | weight_factor | - Required", "- <PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> | JSON | Trọng số cho các câu hỏi | wk_probation.evaluation_forms.weight_factor", "14 | Hạn đ<PERSON>h giá | evaluate_date | DATE | Hạn đánh giá giai đoạn | wk_probation.probation_stages.evaluate_date", "15 | H<PERSON>n phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn | wk_probation.probation_stages.approve_date", "16 | <PERSON><PERSON><PERSON> kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn | wk_probation.probation_stages.default_status", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước | wk_probation.probation.remind_date", "18 | Btn Lưu", "19 | Btn Hủy", "<PERSON><PERSON> tả logic", "STT | Text hiển thị | Tên biến | Mã validate | Kiểu dữ liệu | Ý nghĩa", "1 | Th<PERSON>ng tin nhân sự thử việc", "2 | Ch<PERSON><PERSON> quản lý trực tiếp | direct_manager_id | INT | id quản lý trực tiếp tham chiếu tới bảng employee", "3 | Chọn người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "7 | <PERSON><PERSON><PERSON> bắt đầu | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc", "9 | Btn Xóa giai đo<PERSON>n", "10 | Chọn người đánh giá | evaluator_id | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee", "11 | Chọn mẫu đánh giá | evaluation_template_id | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates", "12 | Cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | weight_factor | JSON | Trọng số cho các câu hỏi", "14 | Hạn đánh giá | evaluate_date | DATE | Hạn đánh giá giai đoạn", "15 | Hạn phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn", "16 | Ch<PERSON>n kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước", "18 | Btn Lưu", "19 | Btn Hủy", "<PERSON><PERSON> tả logic"], "business_flows": ["<PERSON>ô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | N<PERSON><PERSON> dung | Nội dung", "Trạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button L<PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable", "1 | <PERSON><PERSON> lọc | <PERSON><PERSON> lọc | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm", "2 | Tê<PERSON> nhân viên | <PERSON><PERSON><PERSON> nhân viên | <PERSON><PERSON><PERSON> thị Tên nhân viên | <PERSON><PERSON><PERSON> thị Tên nhân viên | <PERSON><PERSON><PERSON> thị Tên nhân viên", "3 | Phòng ban | <PERSON><PERSON>ng ban | Hi<PERSON>n thị phòng ban của nhân viên | Hi<PERSON>n thị phòng ban của nhân viên | <PERSON><PERSON><PERSON> thị phòng ban của nhân viên", "4 | Vị trí | <PERSON><PERSON> trí | <PERSON><PERSON><PERSON> thị vị trí của nhân viên | Hi<PERSON>n thị vị trí của nhân viên | <PERSON><PERSON><PERSON> thị vị trí của nhân viên", "5 | <PERSON><PERSON><PERSON> thức | <PERSON><PERSON><PERSON> thứ<PERSON> | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên", "6 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân viên", "7 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân viên", "8 | Button Chỉnh sửa | Button Chỉnh sửa | Đi<PERSON>u hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc", "<PERSON><PERSON> hình dữ liệu", "<PERSON>ô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | N<PERSON><PERSON> dung | Nội dung", "Trạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable | các button đều enable | các button đều enable", "1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc", "2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp", "3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắ<PERSON> đầu | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc", "9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc", "10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí", "14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn", "15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn", "16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn", "17 | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước", "18 | Btn Lưu | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | Btn Hủy | H<PERSON>y tạo thử việc | <PERSON><PERSON>y tạo thử việc | H<PERSON>y tạo thử việc", "<PERSON><PERSON> hình dữ liệu", "<PERSON>ô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | N<PERSON><PERSON> dung | Nội dung", "Trạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable", "1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc", "2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp", "3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắ<PERSON> đầu | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc", "9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc", "10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí", "14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn", "15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn", "16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn", "17 | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước", "18 | Btn Lưu | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | Btn Hủy | H<PERSON>y tạo thử việc | <PERSON><PERSON>y tạo thử việc | H<PERSON>y tạo thử việc", "<PERSON><PERSON> hình dữ liệu"], "file_type": "api_specification", "content_summary": "<PERSON><PERSON><PERSON> danh sách thử việc | <PERSON><PERSON><PERSON> hình <PERSON> Thử việc: | <PERSON><PERSON><PERSON> hình Chỉnh sửa Thử việc", "key_entities": [], "apis": [], "ui_elements": ["Chỉnh sửa <PERSON> việc", "s.weight_factor", "Chỉnh sửa | Button Chỉnh sửa | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc", "đều enable | các button đều enable | các button đều enable", "đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable", "Chỉnh sửa | - <PERSON><PERSON> admin ấn button 1 thì chuyển đến màn Chỉnh sửa thử việc", "s.evaluation_template_id", "list thử việc", "<PERSON><PERSON><PERSON> gi<PERSON> đ<PERSON><PERSON><PERSON> chọn ở mục 11", "s.evaluator_id", "<PERSON><PERSON><PERSON> việc:", "đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable", "<PERSON><PERSON>i đặt trọng số chấm điểm", "thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn L<PERSON>u cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable", "cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm"]}, "updated_at": "2025-07-31T17:53:20.837233"}, {"id": "502205fd-3a86-494a-b048-815860afb19b", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174035_Gemini.txt", "relative_path": "outputs/aaa_TestCases_20250731_174035_Gemini.txt", "original_name": "aaa_TestCases_20250731_174035_Gemini.txt", "file_type": ".txt", "file_size": 26714, "uploaded_at": "2025-07-31T17:40:35.248937", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:20.883857"}, {"id": "39637c98-d3a0-4b61-8988-03bbae04f56f", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174035_Gemini.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_174035_Gemini.xlsx", "original_name": "aaa_TestCases_20250731_174035_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9257, "uploaded_at": "2025-07-31T17:40:35.248959", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:20.934549"}, {"id": "0552fab2-3438-465c-9029-27a336f1ae62", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174511_Gemini.txt", "relative_path": "outputs/aaa_TestCases_20250731_174511_Gemini.txt", "original_name": "aaa_TestCases_20250731_174511_Gemini.txt", "file_type": ".txt", "file_size": 30937, "uploaded_at": "2025-07-31T17:45:12.365931", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:20.976097"}, {"id": "f9b9925e-46c3-45b0-bcff-e3708a42b832", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174511_Gemini.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_174511_Gemini.xlsx", "original_name": "aaa_TestCases_20250731_174511_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9781, "uploaded_at": "2025-07-31T17:45:12.365992", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:21.006502"}, {"id": "7e9a00a4-a8ea-46ce-92aa-cba825e81c37", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174606_ChatGPT.txt", "relative_path": "outputs/aaa_TestCases_20250731_174606_ChatGPT.txt", "original_name": "aaa_TestCases_20250731_174606_ChatGPT.txt", "file_type": ".txt", "file_size": 32759, "uploaded_at": "2025-07-31T17:46:07.008849", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-31T17:53:21.040725"}, {"id": "d4a8fa3a-2054-4373-a3ff-d9d51d021908", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174606_ChatGPT.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_174606_ChatGPT.xlsx", "original_name": "aaa_TestCases_20250731_174606_ChatGPT.xlsx", "file_type": ".xlsx", "file_size": 9277, "uploaded_at": "2025-07-31T17:46:07.008871", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-31T17:53:21.072759"}, {"id": "884aaa80-63d9-48b3-88ae-a8f89339f8d3", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174638_Gemini.txt", "relative_path": "outputs/aaa_TestCases_20250731_174638_Gemini.txt", "original_name": "aaa_TestCases_20250731_174638_Gemini.txt", "file_type": ".txt", "file_size": 27428, "uploaded_at": "2025-07-31T17:46:38.259065", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:21.104722"}, {"id": "998a92e9-d554-47b9-abeb-900af7642f77", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174638_Gemini.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_174638_Gemini.xlsx", "original_name": "aaa_TestCases_20250731_174638_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9296, "uploaded_at": "2025-07-31T17:46:38.259102", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:21.136763"}, {"id": "2c86f697-5b8e-449b-980a-0cbd61aa3f03", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174930_Gemini.txt", "relative_path": "outputs/aaa_TestCases_20250731_174930_Gemini.txt", "original_name": "aaa_TestCases_20250731_174930_Gemini.txt", "file_type": ".txt", "file_size": 27735, "uploaded_at": "2025-07-31T17:49:31.243871", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:21.179103"}, {"id": "a7140c7b-8e5a-47de-8978-ce008256c092", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174930_Gemini.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_174930_Gemini.xlsx", "original_name": "aaa_TestCases_20250731_174930_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9431, "uploaded_at": "2025-07-31T17:49:31.243894", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:21.210015"}, {"id": "cd667e01-a0c4-4ecf-ba55-19135b435fb1", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175042_ChatGPT.txt", "relative_path": "outputs/aaa_TestCases_20250731_175042_ChatGPT.txt", "original_name": "aaa_TestCases_20250731_175042_ChatGPT.txt", "file_type": ".txt", "file_size": 32759, "uploaded_at": "2025-07-31T17:50:43.414770", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-31T17:53:21.238901"}, {"id": "19c0d33f-7cc1-43d1-b9bb-a96e8958d369", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175042_ChatGPT.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_175042_ChatGPT.xlsx", "original_name": "aaa_TestCases_20250731_175042_ChatGPT.xlsx", "file_type": ".xlsx", "file_size": 9278, "uploaded_at": "2025-07-31T17:50:43.414808", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-31T17:53:21.265249"}, {"id": "39422955-9a23-4699-a1e9-aba553ce2f43", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175136_Gemini.txt", "relative_path": "outputs/aaa_TestCases_20250731_175136_Gemini.txt", "original_name": "aaa_TestCases_20250731_175136_Gemini.txt", "file_type": ".txt", "file_size": 28113, "uploaded_at": "2025-07-31T17:51:37.083984", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:21.292368"}, {"id": "ff879dae-2bdb-4609-92c2-1b36bdee24d0", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175136_Gemini.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_175136_Gemini.xlsx", "original_name": "aaa_TestCases_20250731_175136_Gemini.xlsx", "file_type": ".xlsx", "file_size": 9513, "uploaded_at": "2025-07-31T17:51:37.084007", "processing_status": "processed", "category": "output", "model_used": "Gemini", "updated_at": "2025-07-31T17:53:21.321487"}, {"id": "5ccbaa13-fafa-47b8-bce5-df527f40f87f", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175321_Gemini.txt", "relative_path": "outputs/aaa_TestCases_20250731_175321_Gemini.txt", "original_name": "aaa_TestCases_20250731_175321_Gemini.txt", "file_type": ".txt", "file_size": 37539, "uploaded_at": "2025-07-31T17:53:21.700610", "processing_status": "processed", "category": "output", "model_used": "Gemini"}, {"id": "46bcce0b-215d-4cdb-b40e-988947a91a53", "path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175321_Gemini.xlsx", "relative_path": "outputs/aaa_TestCases_20250731_175321_Gemini.xlsx", "original_name": "aaa_TestCases_20250731_175321_Gemini.xlsx", "file_type": ".xlsx", "file_size": 10309, "uploaded_at": "2025-07-31T17:53:21.700647", "processing_status": "processed", "category": "output", "model_used": "Gemini"}], "generated_outputs": [{"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174035_Gemini.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174035_Gemini.xlsx", "generated_at": "2025-07-31T17:40:35.248964", "model_used": "Gemini"}, {"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174511_Gemini.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174511_Gemini.xlsx", "generated_at": "2025-07-31T17:45:12.366012", "model_used": "Gemini"}, {"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174606_ChatGPT.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174606_ChatGPT.xlsx", "generated_at": "2025-07-31T17:46:07.008874", "model_used": "ChatGPT"}, {"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174638_Gemini.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174638_Gemini.xlsx", "generated_at": "2025-07-31T17:46:38.259109", "model_used": "Gemini"}, {"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174930_Gemini.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_174930_Gemini.xlsx", "generated_at": "2025-07-31T17:49:31.243898", "model_used": "Gemini"}, {"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175042_ChatGPT.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175042_ChatGPT.xlsx", "generated_at": "2025-07-31T17:50:43.414812", "model_used": "ChatGPT"}, {"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175136_Gemini.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175136_Gemini.xlsx", "generated_at": "2025-07-31T17:51:37.084010", "model_used": "Gemini"}, {"txt_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175321_Gemini.txt", "excel_path": "data/projects/aaa_e6b479c5/outputs/aaa_TestCases_20250731_175321_Gemini.xlsx", "generated_at": "2025-07-31T17:53:21.700653", "model_used": "Gemini"}]}, "97f1aa23-0488-4ac6-b7cd-0c410f99d1f0": {"id": "97f1aa23-0488-4ac6-b7cd-0c410f99d1f0", "name": "a", "description": "", "created_at": "2025-07-25T13:13:28.955297", "updated_at": "2025-07-25T13:13:28.955332", "files": [], "generated_outputs": []}, "ab3cd74e-1a0a-443d-905e-372d1b3e4030": {"id": "ab3cd74e-1a0a-443d-905e-372d1b3e4030", "name": "t<PERSON><PERSON>", "description": "bbbb", "created_at": "2025-07-29T18:09:28.123725", "updated_at": "2025-07-29T18:09:28.123742", "files": [], "generated_outputs": []}, "0e9e4955-f17a-4982-aa05-6f7da5cb9720": {"id": "0e9e4955-f17a-4982-aa05-6f7da5cb9720", "name": "y", "description": "bbbb", "created_at": "2025-07-29T18:09:38.703711", "updated_at": "2025-07-29T18:09:38.703718", "files": [], "generated_outputs": []}, "c94e203f-8814-4ac3-a438-3ec9928f07ec": {"id": "c94e203f-8814-4ac3-a438-3ec9928f07ec", "name": "ABC", "description": "1234567890", "created_at": "2025-07-30T10:39:02.504814", "updated_at": "2025-07-30T10:51:40.688950", "files": [{"id": "20ab2a1d-8621-4758-80b8-ced161228663", "path": "data/projects/ABC_c94e203f/inputs/5ce4c704-caeb-4897-8a46-0b488d5eccd7_tạo mới thông tin thử việc 1.docx", "original_name": "tạo mới thông tin thử việc 1.docx", "file_type": ".docx", "file_size": 27772, "uploaded_at": "2025-07-30T10:39:59.844964", "processing_status": "processed", "category": "input", "metadata": {"raw_content": "M<PERSON>n danh sách thử việc\nMàn hình Tạo Thử việc:\n<PERSON><PERSON><PERSON> hình Chỉnh sửa Thử việc\nMô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | Nội dung | Nội dung\nTrạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable trừ button Lưu/Lưu cập nhật disable | các button đều enable trừ button Lưu/Lưu cập nhật disable | các button đều enable trừ button Lưu/Lưu cập nhật disable\n1 | B<PERSON> lọc | Bộ lọc | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm\n2 | Tên nhân viên | Tên nhân viên | Hiển thị Tên nhân viên | Hiển thị Tên nhân viên | Hiển thị Tên nhân viên\n3 | Phòng ban | Phòng ban | Hiển thị phòng ban của nhân viên | Hiển thị phòng ban của nhân viên | Hiển thị phòng ban của nhân viên\n4 | Vị trí | Vị trí | Hiển thị vị trí của nhân viên | Hiển thị vị trí của nhân viên | Hiển thị vị trí của nhân viên\n5 | Hình thức | Hình thức | Hiển thị hình thức làm việc của nhân viên | Hiển thị hình thức làm việc của nhân viên | Hiển thị hình thức làm việc của nhân viên\n6 | Ngày bắt đầu | Ngày bắt đầu | Hiển thị ngày bắt đầu thử việc của nhân viên | Hiển thị ngày bắt đầu thử việc của nhân viên | Hiển thị ngày bắt đầu thử việc của nhân viên\n7 | Ngày kết thúc | Ngày kết thúc | Hiển thị ngày kết thúc thử việc của nhân viên | Hiển thị ngày kết thúc thử việc của nhân viên | Hiển thị ngày kết thúc thử việc của nhân viên\n8 | Button Chỉnh sửa | Button Chỉnh sửa | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc\nMô hình dữ liệu\nSTT | Text hiển thị | Tên biến | Mã validate | Kiểu dữ liệu | Ý nghĩa | Lưu trữ\n1 | Bộ lọc\n2 | Tên nhân viên | name | INT | Người theo dõi tham chiếu tới bảng employee | wk_employee.employee.name\n3 | Phòng ban | title | VARCHAR(255) | Phòng ban của nhân sự thử việc | wk_employee.department.title\n4 | Vị trí | name | VARCHAR(255) | Vị trí làm việc của nhân sự thử việc | wk_employee.employment_position.name\n5 | Hình thức | name | VARCHAR(255) | Hình thức làm việc của nhân sự thử việc | wk_employee.employment_type.name\n6 | Ngày bắt đầu | start_date | DATE | Ngày bắt đầu thử việc | wk_probation.probation.start_date\n7 | Ngày kết thúc | end_date | DATE | Ngày kết thúc thử việc | wk_probation.probation.end_date\nMô tả logic\n1.1 | Tính năng\n1 | Bộ lọc | Lọc theo vị trí\nLọc theo phòng ban\n2 | Tên nhân viên | Hiển thị tên nhân viên thử việc\nKhi click tên nhân viên:\n+ Nếu nhân viên đã có bản ghi thử việc sẽ chuyển đến màn Thông tin thử việc. \n+Nếu không hiện popup \"Bạn có muốn tạo Thông tin Thử việc\" . Click Button Có => chuyển đến màn tạo Thông tin thử việc. Click btn Hủy => Đóng popup\n3 | Phòng ban | Hiển thị phòng ban của nhân viên thử việc\n4 | Vị trí | Hiển thị vị trí làm việc của nhân viên thử việc\n5 | Hình thức | Hiển thị hình thức làm việc của nhân viên thử việc\n6 | Ngày bắt đầu | Hiển thị ngày bắt đầu thử việc của nhân sự\n7 | Ngày kết thúc | Hiển thị ngày kết thúc thử việc của nhân sự\n8 | Button Chỉnh sửa | - Khi admin ấn button 1 thì chuyển đến màn Chỉnh sửa thử việc\nMô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | Nội dung | Nội dung\nTrạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable | các button đều enable | các button đều enable\n1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc\n2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp\n3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc\n6 | Ngày kết thúc | Ngày kết thúc | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5\n7 | Ngày bắt đầu | Ngày bắt đầu | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc\n9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc\n10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\n12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí\n14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn\n15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn\n16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn\n17 | Gửi thông báo nhắc nhở đánh giá trước | Gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước\n18 | Btn Lưu | Btn Lưu | Lưu thông tin thử việc | Lưu thông tin thử việc | Lưu thông tin thử việc\n19 | Btn Hủy | Btn Hủy | Hủy tạo thử việc | Hủy tạo thử việc | Hủy tạo thử việc\nMô hình dữ liệu\nSTT | Text hiển thị | Tên biến | validate | Kiểu dữ liệu | Ý nghĩa | Lưu trữ\n1 | Thông tin nhân sự thử việc\n2 | Chọn quản lý trực tiếp | direct_manager_id | - Required | INT | id quản lý trực tiếp tham chiếu tới bảng employee | wk_probation.probation.direct_manager_id\n3 | Chọn người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee | wk_probation.probation_followers.follower_id\n4 | Ngày bắt đầu thử việc | start_date | - Required | DATE | Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | end_date | - Required | DATE | Ngày kết thúc thử việc\n6 | Ngày kết thúc | end_date | DATE | Ngày kết thúc thử việc\n7 | Ngày bắt đầu | start_date | DATE | Ngày bắt đầu thử việc\n8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc | wk_probation.probation_stages.name\n9 | Btn Xóa giai đoạn\n10 | Chọn người đánh giá | evaluator_id | - Required | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee | wk_probation.evaluation_forms.evaluator_id\n11 | Chọn mẫu đánh giá | evaluation_template_id | - Required | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates | wk_probation.evaluation_forms.evaluation_template_id\n12 | Cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | weight_factor | - Required\n- Nhập nguyên dương | JSON | Trọng số cho các câu hỏi | wk_probation.evaluation_forms.weight_factor\n14 | Hạn đánh giá | evaluate_date | DATE | Hạn đánh giá giai đoạn | wk_probation.probation_stages.evaluate_date\n15 | Hạn phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn | wk_probation.probation_stages.approve_date\n16 | Chọn kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn | wk_probation.probation_stages.default_status\n17 | Gửi thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước | wk_probation.probation.remind_date\n18 | Btn Lưu\n19 | Btn Hủy\nMô tả logic\n1.1 | Tính năng\n1 | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, hình thức làm việc\n2 | Chọn quản lý trực tiếp | - Được phép chọn duy nhất 1 quản lý trực tiếp\n- Quản lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc\n- Quản lý trực tiếp sẽ là người phê duyệt giai đoạn\n3 | Chọn người theo dõi | - Được phép chọn nhiều người theo dõi\n- Người theo dõi sẽ có quyền xem chi tiết thử việc\n- Người theo dõi sẽ nhận được thông báo khi có người cmt vào thử việc\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu quá trình thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc quá trình thử việc\n6 | Ngày kết thúc | Hiển thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5\n7 | Ngày bắt đầu | Hiển thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới\n9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa\n10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn\nCó thể chọn nhiều người đánh giá cho cùng 1 giai đoạn\nNgười đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\nNgười đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá được chọn\n12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm\nSẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11\n13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi\ntrọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc\n14 | Hạn đánh giá | Ô input nhập hạn đánh giá\nHết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc\n15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn\nHết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc\nKhi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16\n16 | Chọn kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt\nNếu chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo\nNếu chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuyển nhân sự sang tab ending\n17 | Gửi thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá\nKhi trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá\n18 | Btn Lưu | Lưu thông tin thử việc\n19 | Btn Hủy | Hủy tạo mới thử việc , quay trở lại màn hình list thử việc\nMô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | Nội dung | Nội dung\nTrạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable\n1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc\n2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp\n3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc | Ô input nhập Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc | Ô input nhập Ngày kết thúc thử việc\n6 | Ngày kết thúc | Ngày kết thúc | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5 | Hiển thị ngày kết thúc được nhập ở mục 5\n7 | Ngày bắt đầu | Ngày bắt đầu | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4 | Hiển thị ngày kết thúc được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc\n9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc\n10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\n12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí\n14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn\n15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn\n16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn\n17 | Gửi thông báo nhắc nhở đánh giá trước | Gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước\n18 | Btn Lưu | Btn Lưu | Lưu thông tin thử việc | Lưu thông tin thử việc | Lưu thông tin thử việc\n19 | Btn Hủy | Btn Hủy | Hủy tạo thử việc | Hủy tạo thử việc | Hủy tạo thử việc\nMô hình dữ liệu\nSTT | Text hiển thị | Tên biến | Mã validate | Kiểu dữ liệu | Ý nghĩa\n1 | Thông tin nhân sự thử việc\n2 | Chọn quản lý trực tiếp | direct_manager_id | INT | id quản lý trực tiếp tham chiếu tới bảng employee\n3 | Chọn người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee\n4 | Ngày bắt đầu thử việc | start_date | DATE | Ngày bắt đầu thử việc\n5 | Ngày kết thúc thử việc | end_date | DATE | Ngày kết thúc thử việc\n6 | Ngày kết thúc | end_date | DATE | Ngày kết thúc thử việc\n7 | Ngày bắt đầu | start_date | DATE | Ngày bắt đầu thử việc\n8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc\n9 | Btn Xóa giai đoạn\n10 | Chọn người đánh giá | evaluator_id | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee\n11 | Chọn mẫu đánh giá | evaluation_template_id | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates\n12 | Cài đặt trọng số chấm điểm\n13 | Input nhập trọng số | weight_factor | JSON | Trọng số cho các câu hỏi\n14 | Hạn đánh giá | evaluate_date | DATE | Hạn đánh giá giai đoạn\n15 | Hạn phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn\n16 | Chọn kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn\n17 | Gửi thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước\n18 | Btn Lưu\n19 | Btn Hủy\nMô tả logic\n1.1 | Tính năng\n1 | Thông tin nhân sự thử việc | Hiển thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, hình thức làm việc\n2 | Chọn quản lý trực tiếp | - Được phép chọn duy nhất 1 quản lý trực tiếp\n- Quản lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc\n- Quản lý trực tiếp sẽ là người phê duyệt giai đoạn\n3 | Chọn người theo dõi | - Được phép chọn nhiều người theo dõi\n- Người theo dõi sẽ có quyền xem chi tiết thử việc\n- Người theo dõi sẽ nhận được thông báo khi có người cmt vào thử việc\n4 | Ngày bắt đầu thử việc | Ngày bắt đầu quá trình thử việc\n5 | Ngày kết thúc thử việc | Ngày kết thúc quá trình thử việc\n6 | Ngày kết thúc | Hiển thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5\n7 | Ngày bắt đầu | Hiển thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4\n8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới\n9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa\nBtn Xóa giai đoạn chỉ hiện ở những giai đoạn nào chưa có kết quả đánh giá/phê duyệt\n10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn\nCó thể chọn nhiều người đánh giá cho cùng 1 giai đoạn\nNgười đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng\n11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá\nNgười đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá được chọn\n12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm\nSẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11\n13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi\ntrọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc\n14 | Hạn đánh giá | Ô input nhập hạn đánh giá\nHết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc\n15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn\nHết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc\nKhi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16\n16 | Chọn kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt\nNếu chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo\nNếu chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuyển nhân sự sang tab ending\n17 | Gửi thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá\nKhi trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá\n18 | Btn Lưu | Lưu thông tin thử việc\n19 | Btn Hủy | Hủy tạo mới thử việc , quay trở lại màn hình list thử việc", "functions": ["1.1 | <PERSON><PERSON><PERSON> n<PERSON>ng", "1 | <PERSON><PERSON> lọc | <PERSON>ọc theo vị trí", "<PERSON><PERSON><PERSON> theo phòng ban", "2 | T<PERSON><PERSON> nhân viên | <PERSON><PERSON><PERSON> thị tên nhân viên thử việc", "<PERSON>hi click tên nhân viên:", "+ <PERSON><PERSON><PERSON> nhân viên đã có bản ghi thử việc sẽ chuyển đến màn Thông tin thử việc.", "+<PERSON><PERSON><PERSON> không hiện popup \"Bạn có muốn tạo Thông tin Thử việc\" . Click Button Có => chuyển đến màn tạo Thông tin thử việc. Click btn Hủy => Đóng popup", "3 | <PERSON><PERSON>ng ban | <PERSON><PERSON><PERSON> thị phòng ban của nhân viên thử việc", "4 | <PERSON><PERSON> trí | <PERSON><PERSON><PERSON> thị vị trí làm việc của nhân viên thử việc", "5 | <PERSON><PERSON><PERSON> thức | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên thử việc", "6 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân sự", "7 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân sự", "8 | Button Chỉnh sửa | - <PERSON><PERSON> admin ấn button 1 thì chuyển đến màn Chỉnh sửa thử việc", "1.1 | <PERSON><PERSON><PERSON> n<PERSON>ng", "1 | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, h<PERSON><PERSON> thức làm việc", "2 | Chọn quản lý trực tiếp | - <PERSON><PERSON><PERSON><PERSON> phép chọn duy nhất 1 quản lý trực tiếp", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ là người phê duyệt giai đoạn", "3 | Ch<PERSON><PERSON> người theo dõi | - <PERSON><PERSON><PERSON><PERSON> phép chọn nhiều người theo dõi", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ có quyền xem chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ nhận đư<PERSON><PERSON> thông báo khi có người cmt vào thử việc", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu quá trình thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc quá trình thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới", "9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa", "10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn", "<PERSON><PERSON> thể chọn nhiều người đánh giá cho cùng 1 giai đoạn", "Người đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "Người đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá đượ<PERSON> chọn", "12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm", "Sẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11", "13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi", "trọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc", "14 | Hạn đánh giá | Ô input nhập hạn đánh giá", "Hết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc", "15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn", "<PERSON>ết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc", "<PERSON><PERSON> hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16", "16 | Ch<PERSON><PERSON> kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt", "<PERSON><PERSON><PERSON> chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo", "<PERSON><PERSON><PERSON> chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuy<PERSON>n nhân sự sang tab ending", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá", "<PERSON><PERSON> trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá", "18 | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | <PERSON><PERSON><PERSON> tạo mới thử việc , quay trở lại màn hình list thử việc", "1.1 | <PERSON><PERSON><PERSON> n<PERSON>ng", "1 | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc bao gồm : tên nhân sự, email, phòng ban, vị trí, h<PERSON><PERSON> thức làm việc", "2 | Chọn quản lý trực tiếp | - <PERSON><PERSON><PERSON><PERSON> phép chọn duy nhất 1 quản lý trực tiếp", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ có quyền chỉnh sửa chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> lý trực tiếp sẽ là người phê duyệt giai đoạn", "3 | Ch<PERSON><PERSON> người theo dõi | - <PERSON><PERSON><PERSON><PERSON> phép chọn nhiều người theo dõi", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ có quyền xem chi tiết thử việc", "- <PERSON><PERSON><PERSON><PERSON> theo dõi sẽ nhận đư<PERSON><PERSON> thông báo khi có người cmt vào thử việc", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu quá trình thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc quá trình thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc quá trình thử việc đã được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu quá trình thử việc đã được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Nhập tên giai đoạn sau đó nhấn btn Thêm ( dấu cộng ) giai đoạn mới sẽ được hiển thị ở phía dưới", "9 | Btn Xóa giai đoạn | Click btn Xóa thì giai đoạn tương ứng sẽ bị xóa", "Btn Xóa giai đoạn chỉ hiện ở những giai đoạn nào chưa có kết quả đánh giá/phê duyệt", "10 | Chọn người đánh giá | Chọn người đánh giá cho giai đoạn", "<PERSON><PERSON> thể chọn nhiều người đánh giá cho cùng 1 giai đoạn", "Người đánh giá sẽ có quyền đánh giá nhân sự thử việc trong giai đoạn tương ứng", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "Người đánh giá sẽ đánh giá nhân sự thử việc dựa vào mẫu đánh giá đượ<PERSON> chọn", "12 | Cài đặt trọng số chấm điểm | Ấn btn này sẽ hiện thị form Cài đặt trọng số chấm điểm", "Sẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11", "13 | Input nhập trọng số | Ô input nhập trọng số cho các câu hỏi", "trọng số sẽ được dùng để tính điểm trung bình của Phù hợp với tổ chức và Phù hợp với công việc", "14 | Hạn đánh giá | Ô input nhập hạn đánh giá", "Hết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc", "15 | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn", "<PERSON>ết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc", "<PERSON><PERSON> hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt thì hệ thống sẽ tự động phê duyệt dựa theo option được chọn ở mục 16", "16 | Ch<PERSON><PERSON> kết quả mặc định | Kết quả phê duyệt giai đoạn mặc định khi hết hạn phê duyệt mà người quản lý trực tiếp chưa phê duyệt", "<PERSON><PERSON><PERSON> chọn Pass thì hệ thống sẽ phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo", "<PERSON><PERSON><PERSON> chọn Fail thì hệ thống sẽ phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuy<PERSON>n nhân sự sang tab ending", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước so với hạn đánh giá", "<PERSON><PERSON> trước hạn đánh giá số ngày đã chọn thì sẽ gửi thông báo đến người đánh giá", "18 | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | <PERSON><PERSON><PERSON> tạo mới thử việc , quay trở lại màn hình list thử việc"], "requirements": [], "validations": ["STT | Text hiển thị | Tê<PERSON> biến | Mã validate | Kiểu dữ liệu | Ý nghĩa | <PERSON><PERSON><PERSON> trữ", "1 | <PERSON><PERSON> lọc", "2 | Tê<PERSON> nhân viên | name | INT | Người theo dõi tham chiếu tới bảng employee | wk_employee.employee.name", "3 | Phòng ban | title | VARCHAR(255) | Phòng ban của nhân sự thử việc | wk_employee.department.title", "4 | V<PERSON> trí | name | VARCHAR(255) | Vị trí làm việc của nhân sự thử việc | wk_employee.employment_position.name", "5 | <PERSON><PERSON><PERSON> thức | name | VARCHAR(255) | <PERSON><PERSON><PERSON> thức làm việc của nhân sự thử việc | wk_employee.employment_type.name", "6 | <PERSON><PERSON><PERSON> bắt đầu | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc | wk_probation.probation.start_date", "7 | <PERSON><PERSON><PERSON> kết thúc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc | wk_probation.probation.end_date", "<PERSON><PERSON> tả logic", "STT | Text hiển thị | Tê<PERSON> biến | validate | <PERSON><PERSON><PERSON> dữ liệu | Ý nghĩa | <PERSON><PERSON><PERSON> trữ", "1 | Th<PERSON>ng tin nhân sự thử việc", "2 | Ch<PERSON><PERSON> quản lý trực tiếp | direct_manager_id | - Required | INT | id quản lý trực tiếp tham chi<PERSON>u tới bảng employee | wk_probation.probation.direct_manager_id", "3 | Ch<PERSON>n người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee | wk_probation.probation_followers.follower_id", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | start_date | - Required | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | end_date | - Required | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "7 | <PERSON><PERSON><PERSON> bắt đầu | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc | wk_probation.probation_stages.name", "9 | Btn Xóa giai đo<PERSON>n", "10 | Chọn người đánh giá | evaluator_id | - Required | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee | wk_probation.evaluation_forms.evaluator_id", "11 | Chọn mẫu đánh giá | evaluation_template_id | - Required | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates | wk_probation.evaluation_forms.evaluation_template_id", "12 | Cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | weight_factor | - Required", "- <PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> | JSON | Trọng số cho các câu hỏi | wk_probation.evaluation_forms.weight_factor", "14 | Hạn đ<PERSON>h giá | evaluate_date | DATE | Hạn đánh giá giai đoạn | wk_probation.probation_stages.evaluate_date", "15 | H<PERSON>n phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn | wk_probation.probation_stages.approve_date", "16 | <PERSON><PERSON><PERSON> kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn | wk_probation.probation_stages.default_status", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước | wk_probation.probation.remind_date", "18 | Btn Lưu", "19 | Btn Hủy", "<PERSON><PERSON> tả logic", "STT | Text hiển thị | Tên biến | Mã validate | Kiểu dữ liệu | Ý nghĩa", "1 | Th<PERSON>ng tin nhân sự thử việc", "2 | Ch<PERSON><PERSON> quản lý trực tiếp | direct_manager_id | INT | id quản lý trực tiếp tham chiếu tới bảng employee", "3 | Chọn người theo dõi | follower_id | INT | id người theo dõi tham chiếu tới bảng employee", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | end_date | DATE | <PERSON><PERSON><PERSON> kết thúc thử việc", "7 | <PERSON><PERSON><PERSON> bắt đầu | start_date | DATE | <PERSON><PERSON><PERSON> bắt đầu thử việc", "8 | Btn Thêm giai đoạn | name | VARCHAR | Giai đoạn thử việc", "9 | Btn Xóa giai đo<PERSON>n", "10 | Chọn người đánh giá | evaluator_id | INT | id người đánh giá giai đoạn tham chiếu tới bảng employee", "11 | Chọn mẫu đánh giá | evaluation_template_id | INT | id mẫu đánh giá tham chiếu tới bảng wk_probation.evaluation_templates", "12 | Cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | weight_factor | JSON | Trọng số cho các câu hỏi", "14 | Hạn đánh giá | evaluate_date | DATE | Hạn đánh giá giai đoạn", "15 | Hạn phê duyệt | approve_date | DATE | Hạn phê duyệt giai đoạn", "16 | Ch<PERSON>n kết quả mặc định | default_status | ENUM | Kết quả mặc định phê duyệt giai đoạn", "17 | <PERSON><PERSON><PERSON> thông báo nhắc nhở đánh giá trước | remind_date | ENUM | số ngày gửi thông báo nhắc nhở đánh giá trước", "18 | Btn Lưu", "19 | Btn Hủy", "<PERSON><PERSON> tả logic"], "business_flows": ["<PERSON>ô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | N<PERSON><PERSON> dung | Nội dung", "Trạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button L<PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable", "1 | <PERSON><PERSON> lọc | <PERSON><PERSON> lọc | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm | Ô input nhập điều kiện tìm kiếm", "2 | Tê<PERSON> nhân viên | <PERSON><PERSON><PERSON> nhân viên | <PERSON><PERSON><PERSON> thị Tên nhân viên | <PERSON><PERSON><PERSON> thị Tên nhân viên | <PERSON><PERSON><PERSON> thị Tên nhân viên", "3 | Phòng ban | <PERSON><PERSON>ng ban | Hi<PERSON>n thị phòng ban của nhân viên | Hi<PERSON>n thị phòng ban của nhân viên | <PERSON><PERSON><PERSON> thị phòng ban của nhân viên", "4 | Vị trí | <PERSON><PERSON> trí | <PERSON><PERSON><PERSON> thị vị trí của nhân viên | Hi<PERSON>n thị vị trí của nhân viên | <PERSON><PERSON><PERSON> thị vị trí của nhân viên", "5 | <PERSON><PERSON><PERSON> thức | <PERSON><PERSON><PERSON> thứ<PERSON> | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên | <PERSON><PERSON><PERSON> thị hình thức làm việc của nhân viên", "6 | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày bắt đầu thử việc của nhân viên", "7 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân viên | <PERSON><PERSON><PERSON> thị ngày kết thúc thử việc của nhân viên", "8 | Button Chỉnh sửa | Button Chỉnh sửa | Đi<PERSON>u hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc", "<PERSON><PERSON> hình dữ liệu", "<PERSON>ô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | N<PERSON><PERSON> dung | Nội dung", "Trạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable | các button đều enable | các button đều enable", "1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc", "2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp", "3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắ<PERSON> đầu | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc", "9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc", "10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí", "14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn", "15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn", "16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn", "17 | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước", "18 | Btn Lưu | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | Btn Hủy | H<PERSON>y tạo thử việc | <PERSON><PERSON>y tạo thử việc | H<PERSON>y tạo thử việc", "<PERSON><PERSON> hình dữ liệu", "<PERSON>ô tả luồng xử lý | STT | Luồng xử lý | Luồng xử lý | Nội dung | N<PERSON><PERSON> dung | Nội dung", "Trạng thái ban đầu (khi loading lần đầu tiên) | Trạng thái ban đầu (khi loading lần đầu tiên) | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable", "1 | Thông tin nhân sự thử việc | Thông tin nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc | <PERSON><PERSON><PERSON> thị thông tin của nhân sự thử việc", "2 | Chọn quản lý trực tiếp | Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp | Ô input Chọn quản lý trực tiếp", "3 | Chọn người theo dõi | Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi | Ô input Chọn người theo dõi", "4 | <PERSON><PERSON><PERSON> bắt đầu thử việc | <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc | Ô input nhập <PERSON><PERSON><PERSON> bắt đầu thử việc", "5 | <PERSON><PERSON><PERSON> kết thúc thử việc | <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc | Ô input nhập <PERSON><PERSON><PERSON> kết thúc thử việc", "6 | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> kết thúc | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 5", "7 | <PERSON><PERSON><PERSON> bắ<PERSON> đầu | <PERSON><PERSON><PERSON> bắt đầu | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4 | <PERSON><PERSON><PERSON> thị ngày kết thúc được nhập ở mục 4", "8 | Btn Thêm giai đoạn | Btn Thêm giai đoạn | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc | Thêm mới 1 giai đoạn thử việc", "9 | Btn Xóa giai đoạn | Btn Xóa giai đoạn | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc | Xóa giai đoạn thử việc", "10 | Chọn người đánh giá | Chọn người đánh giá | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn | Chọn người đánh giá trong giai đoạn", "11 | Chọn mẫu đánh giá | Chọn mẫu đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá | Chọn mẫu đánh giá cho từng người đánh giá", "12 | Cài đặt trọng số chấm điểm | Cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm", "13 | Input nhập trọng số | Input nhập trọng số | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí | Ô input nhập trọng số cho các tiêu chí", "14 | Hạn đánh giá | Hạn đánh giá | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn | Ô input nhập hạn đánh giá giai đoạn", "15 | Hạn phê duyệt | Hạn phê duyệt | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn | Ô input nhập hạn phê duyệt giai đoạn", "16 | Chọn kết quả mặc định | Chọn kết quả mặc định | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn | Chọn kết quả mặc định phê duyệt cho giai đoạn", "17 | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | G<PERSON><PERSON> thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước | Input chọn số ngày gửi thông báo nhắc nhở đánh giá trước", "18 | Btn Lưu | Btn Lưu | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc | <PERSON><PERSON><PERSON> thông tin thử việc", "19 | Btn Hủy | Btn Hủy | H<PERSON>y tạo thử việc | <PERSON><PERSON>y tạo thử việc | H<PERSON>y tạo thử việc", "<PERSON><PERSON> hình dữ liệu"], "file_type": "api_specification", "content_summary": "<PERSON><PERSON><PERSON> danh sách thử việc | <PERSON><PERSON><PERSON> hình <PERSON> Thử việc: | <PERSON><PERSON><PERSON> hình Chỉnh sửa Thử việc", "key_entities": [], "apis": [], "ui_elements": ["đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable | các button đều enable trừ button <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> cập nhật disable", "<PERSON><PERSON><PERSON> việc:", "<PERSON><PERSON>i đặt trọng số chấm điểm", "đều enable | các button đều enable | các button đều enable", "Chỉnh sửa <PERSON> việc", "s.evaluator_id", "s.weight_factor", "list thử việc", "<PERSON><PERSON><PERSON> gi<PERSON> đ<PERSON><PERSON><PERSON> chọn ở mục 11", "thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn L<PERSON>u cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable", "Có => chuyển đến màn tạo Thông tin thử việc. Click btn Hủy => <PERSON><PERSON><PERSON> popup", "Chỉnh sửa | - <PERSON><PERSON> admin ấn button 1 thì chuyển đến màn Chỉnh sửa thử việc", "Chỉnh sửa | Button Chỉnh sửa | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc | Điều hướng đến màn chỉnh sửa thông tin thử việc", "đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable | các button đều enable , btn <PERSON><PERSON><PERSON> cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable", "cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm | Btn hiển thị form cài đặt trọng số chấm điểm"]}, "updated_at": "2025-07-30T10:51:40.629648"}, {"id": "5ad859b6-0c6c-4467-9a9e-aaacabbd672e", "path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_104049_ChatGPT.txt", "relative_path": "outputs/ABC_TestCases_20250730_104049_ChatGPT.txt", "original_name": "ABC_TestCases_20250730_104049_ChatGPT.txt", "file_type": ".txt", "file_size": 7733, "uploaded_at": "2025-07-30T10:40:49.392453", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-30T10:51:40.636475"}, {"id": "840679b4-3dba-4e96-a5f3-dda9a6bb3ffb", "path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_104049_ChatGPT.xlsx", "relative_path": "outputs/ABC_TestCases_20250730_104049_ChatGPT.xlsx", "original_name": "ABC_TestCases_20250730_104049_ChatGPT.xlsx", "file_type": ".xlsx", "file_size": 7219, "uploaded_at": "2025-07-30T10:40:49.392468", "processing_status": "processed", "category": "output", "model_used": "ChatGPT", "updated_at": "2025-07-30T10:51:40.642394"}, {"id": "7d76e981-4044-4ac3-8dee-259cbff29b31", "path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_105140_Gemini.txt", "relative_path": "outputs/ABC_TestCases_20250730_105140_Gemini.txt", "original_name": "ABC_TestCases_20250730_105140_Gemini.txt", "file_type": ".txt", "file_size": 22885, "uploaded_at": "2025-07-30T10:51:40.688928", "processing_status": "processed", "category": "output", "model_used": "Gemini"}, {"id": "dee684dc-f927-42a3-a377-782e056bd000", "path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_105140_Gemini.xlsx", "relative_path": "outputs/ABC_TestCases_20250730_105140_Gemini.xlsx", "original_name": "ABC_TestCases_20250730_105140_Gemini.xlsx", "file_type": ".xlsx", "file_size": 8916, "uploaded_at": "2025-07-30T10:51:40.688942", "processing_status": "processed", "category": "output", "model_used": "Gemini"}], "generated_outputs": [{"txt_path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_104049_ChatGPT.txt", "excel_path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_104049_ChatGPT.xlsx", "generated_at": "2025-07-30T10:40:49.392471", "model_used": "ChatGPT"}, {"txt_path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_105140_Gemini.txt", "excel_path": "data/projects/ABC_c94e203f/outputs/ABC_TestCases_20250730_105140_Gemini.xlsx", "generated_at": "2025-07-30T10:51:40.688945", "model_used": "Gemini"}]}}