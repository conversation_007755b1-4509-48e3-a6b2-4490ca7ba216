{"526e2dd9-c9c2-40bf-a093-f3d57844fc98": {"id": "526e2dd9-c9c2-40bf-a093-f3d57844fc98", "name": "User Management System", "description": "Demo project for enhanced multi-file workflow", "created_at": "2025-07-29T16:18:44.116087", "updated_at": "2025-07-29T16:18:44.124653", "files": [{"id": "3fcf3740-26f4-417a-9104-f7433a967b60", "path": "/tmp/tmp482613nz/api_spec.txt", "original_name": "api_spec.txt", "file_type": ".txt", "file_size": 809, "uploaded_at": "2025-07-29T16:18:44.122351", "processing_status": "pending", "category": "input", "metadata": {"raw_content": "API Specification - User Management System\n\nEndpoints:\n1. POST /api/users\n   - <PERSON><PERSON>o người dùng mới\n   - Input: name, email, password, role\n   - Validation: email unique, password min 8 chars\n   - Output: user object with ID\n\n2. GET /api/users/{id}\n   - <PERSON><PERSON><PERSON> thông tin người dùng theo ID\n   - Authorization: required\n   - Output: user object hoặc 404 error\n\n3. PUT /api/users/{id}\n   - <PERSON><PERSON><PERSON> nhật thông tin người dùng\n   - Authorization: owner hoặc admin\n   - Input: name, email, role (optional)\n   - Output: updated user object\n\n4. DELETE /api/users/{id}\n   - X<PERSON>a người dùng\n   - Authorization: admin only\n   - Output: success message\n\nAuthentication:\n- JWT token required for all endpoints except POST /api/users\n- Token expires after 24 hours\n- Refresh token mechanism available", "functions": [], "requirements": ["API Specification - User Management System", "Endpoints:", "1. POST /api/users", "- <PERSON><PERSON><PERSON> ng<PERSON>ời dùng mới", "- Input: name, email, password, role"], "validations": ["- Validation: email unique, password min 8 chars", "- Output: user object with ID", "2. GET /api/users/{id}", "- <PERSON><PERSON><PERSON> thông tin người dùng theo ID", "- Authorization: required", "- Output: user object hoặc 404 error", "3. PUT /api/users/{id}", "- <PERSON><PERSON><PERSON> nh<PERSON>t thông tin người dùng", "- Authorization: owner hoặc admin", "- Input: name, email, role (optional)", "- Output: updated user object", "4. DELETE /api/users/{id}", "- <PERSON><PERSON><PERSON> ng<PERSON>ời dùng", "- Authorization: admin only", "- Output: success message", "Authentication:", "- JWT token required for all endpoints except POST /api/users", "- Token expires after 24 hours", "- Refresh token mechanism available"], "business_flows": [], "file_type": "api_specification", "content_summary": "API Specification - User Management System | 1. POST /api/users | - T<PERSON>o người dùng mới", "key_entities": ["ng<PERSON><PERSON><PERSON> dùng ", "ng<PERSON><PERSON><PERSON> dùng\n   ", "user", "user ", "User Management"], "apis": ["POST /api/users", "GET /api/users/{id}", "PUT /api/users/{id}", "DELETE /api/users/{id}", "POST /api/users", "/api/users", "/api/users/{id}", "/api/users/{id}", "/api/users/{id}", "/api/users"], "ui_elements": []}}, {"id": "4cb62e66-2fa0-4f5f-b309-7181371f34aa", "path": "/tmp/tmp482613nz/ui_requirements.txt", "original_name": "ui_requirements.txt", "file_type": ".txt", "file_size": 864, "uploaded_at": "2025-07-29T16:18:44.123186", "processing_status": "pending", "category": "input", "metadata": {"raw_content": "User Interface Requirements - User Management\n\nMàn hình danh sách người dùng:\n- Hiển thị bảng với các cột: ID, Tê<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> trò, <PERSON><PERSON><PERSON> tạo\n- <PERSON><PERSON> trang: 20 users per page\n- Tìm kiếm theo tên hoặc email\n- Filter theo vai trò (<PERSON><PERSON>, User, Guest)\n- <PERSON><PERSON><PERSON> \"Thêm người dùng mới\"\n- <PERSON><PERSON><PERSON> \"Xuất Excel\" cho danh sách hiện tại\n\nForm thêm/sửa người dùng:\n- Trường bắt buộc: T<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> khẩu (chỉ khi thêm mới)\n- Dropdown chọn vai trò\n- Validation real-time:\n  * Email format validation\n  * Password strength indicator\n  * Duplicate email check\n- Nút Lưu và Hủy\n- Hiển thị thông báo thành công/lỗi\n\nChức năng xóa:\n- Popup xác nhận trước khi xóa\n- Không cho phép xóa chính mình\n- Chỉ admin mới được xóa user khác\n- <PERSON><PERSON><PERSON> thị lý do nếu không thể xóa", "functions": ["<PERSON><PERSON><PERSON> n<PERSON>ng xóa:", "- Popup x<PERSON>c nh<PERSON>n trư<PERSON>c khi xóa", "- <PERSON><PERSON><PERSON><PERSON> cho phép xóa ch<PERSON>h mình", "- Chỉ admin mới đ<PERSON> xóa user khác", "- <PERSON><PERSON><PERSON> thị lý do nếu không thể xóa"], "requirements": ["User Interface Requirements - User Management", "<PERSON><PERSON><PERSON> hình danh sách người dùng:", "- <PERSON><PERSON><PERSON> thị bảng với các cột: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> t<PERSON>o", "- Phân trang: 20 users per page", "- <PERSON><PERSON><PERSON> kiếm theo tên hoặc email", "- Filter theo vai trò (<PERSON><PERSON>, User, Guest)", "- <PERSON><PERSON><PERSON> \"Thêm người dùng mới\"", "- <PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" cho danh sách hiện tại", "Form thêm/sửa người dùng:", "- <PERSON><PERSON><PERSON><PERSON><PERSON> bắt buộc: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (chỉ khi thêm mới)", "- Dropdown chọn vai trò"], "validations": ["- Validation real-time:", "* Email format validation", "* Password strength indicator", "* Duplicate email check", "- <PERSON><PERSON><PERSON> v<PERSON>", "- <PERSON><PERSON><PERSON> thị thông báo thành công/lỗi"], "business_flows": [], "file_type": "ui_requirements", "content_summary": "User Interface Requirements - User Management | <PERSON><PERSON><PERSON> hình danh sách người dùng: | - <PERSON><PERSON><PERSON> thị bảng với các cột: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> t<PERSON>o", "key_entities": ["ng<PERSON><PERSON><PERSON> dùng", "User", "ng<PERSON><PERSON><PERSON> dùng ", "User ", "user", "user ", "User Management"], "apis": [], "ui_elements": ["\"Xuất Excel\" cho danh sách hiện tại", "at validation", "bắt buộc: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (chỉ khi thêm mới)", "danh s<PERSON>ch ngư<PERSON>i dùng:", "thêm/sửa người dùng:", "\"Thêm người dùng mới\"", "<PERSON>ưu v<PERSON>ủ<PERSON>"]}}, {"id": "8685fee5-c70b-48f2-a461-3a820ddccd9f", "path": "/tmp/tmp482613nz/business_logic.txt", "original_name": "business_logic.txt", "file_type": ".txt", "file_size": 822, "uploaded_at": "2025-07-29T16:18:44.124647", "processing_status": "pending", "category": "input", "metadata": {"raw_content": "Business Logic - User Management System\n\nUser Registration Process:\n1. Validate input data\n2. Check email uniqueness\n3. Hash password with salt\n4. Assign default role (User)\n5. Send welcome email\n6. Log registration event\n\nUser Authentication:\n1. Validate email/password\n2. Check account status (active/inactive)\n3. Generate JWT token\n4. Update last login timestamp\n5. Log login event\n\nRole-based Access Control:\n- Admin: Full access to all users\n- Manager: Can view and edit users in same department\n- User: Can only view/edit own profile\n\nPassword Policy:\n- Minimum 8 characters\n- Must contain uppercase, lowercase, number\n- Cannot reuse last 5 passwords\n- Expires every 90 days for admin accounts\n\nAccount Lockout:\n- Lock after 5 failed login attempts\n- Auto-unlock after 30 minutes\n- <PERSON><PERSON> can manually unlock accounts", "functions": [], "requirements": [], "validations": ["1. Validate input data", "2. Check email uniqueness", "3. Hash password with salt", "4. Assign default role (User)", "5. Send welcome email", "6. Log registration event", "User Authentication:", "1. Validate email/password", "2. Check account status (active/inactive)", "3. Generate JWT token", "4. Update last login timestamp", "5. Log login event", "Role-based Access Control:", "- Admin: Full access to all users", "- Manager: Can view and edit users in same department", "- User: Can only view/edit own profile", "Password Policy:", "- Minimum 8 characters", "- Must contain uppercase, lowercase, number", "- Cannot reuse last 5 passwords", "- Expires every 90 days for admin accounts", "Account Lockout:", "- Lock after 5 failed login attempts", "- Auto-unlock after 30 minutes", "- Admin can manually unlock accounts"], "business_flows": [], "file_type": "api_specification", "content_summary": "Business Logic - User Management System | User Registration Process: | 1. Validate input data", "key_entities": ["User ", "User", "User Management", "user"], "apis": [], "ui_elements": []}}], "generated_outputs": [], "file_relationships": [{"file1_id": "3fcf3740-26f4-417a-9104-f7433a967b60", "file2_id": "4cb62e66-2fa0-4f5f-b309-7181371f34aa", "relationship_score": 1.0, "relationship_type": "api_ui_integration"}, {"file1_id": "3fcf3740-26f4-417a-9104-f7433a967b60", "file2_id": "8685fee5-c70b-48f2-a461-3a820ddccd9f", "relationship_score": 0.****************, "relationship_type": "functional_overlap"}, {"file1_id": "4cb62e66-2fa0-4f5f-b309-7181371f34aa", "file2_id": "8685fee5-c70b-48f2-a461-3a820ddccd9f", "relationship_score": 0.9, "relationship_type": "functional_overlap"}]}}