#!/usr/bin/env python3
"""
YouTube Audio Downloader - Simple Interface
Tải audio từ YouTube với giao diện đơn giản
"""

import sys
from youtube_audio_downloader import YouTubeAudioDownloader

def main():
    """Main function"""
    print("🎵 YouTube Audio Downloader")
    print("=" * 40)
    
    # Tạo downloader với thư mục youtube_audio
    downloader = YouTubeAudioDownloader("youtube_audio")
    
    if len(sys.argv) > 1:
        # Command line mode
        url = sys.argv[1]
        custom_name = sys.argv[2] if len(sys.argv) > 2 else None
        
        print(f"🔗 URL: {url}")
        if custom_name:
            print(f"📝 Tên file: {custom_name}")
        
        success = downloader.download_audio(url, custom_name, debug=True)
        
        if success:
            print("\n🎉 Tải thành công!")
        else:
            print("\n😞 Tải thất bại!")
            print("💡 Thử:")
            print("   - <PERSON><PERSON><PERSON> tra URL")
            print("   - <PERSON><PERSON> dụng VPN")
            print("   - Thử lại sau")
    else:
        # Interactive mode
        print("Nhập URL YouTube để tải audio")
        print("Hoặc 'quit' để thoát\n")
        
        while True:
            url = input("🔗 URL: ").strip()
            
            if url.lower() in ['quit', 'exit', 'q', '']:
                print("👋 Tạm biệt!")
                break
            
            # Hỏi tên file tùy chỉnh
            custom_name = input("📝 Tên file (Enter để dùng tên gốc): ").strip()
            custom_name = custom_name if custom_name else None
            
            print(f"\n🎵 Đang tải...")
            success = downloader.download_audio(url, custom_name, debug=False)
            
            if success:
                print("🎉 Tải thành công!\n")
            else:
                print("😞 Tải thất bại!\n")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Tạm biệt!")
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        print("💡 Đảm bảo đã cài đặt: pip install yt-dlp")
