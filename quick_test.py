#!/usr/bin/env python3
"""
Quick test for image_resizer fix
"""

import cv2
import numpy as np
from image_resizer import add_morning_vibes_text

def quick_test():
    """Test nhanh chức năng thêm text"""
    print("🧪 Testing text function after fix...")
    
    # Tạo ảnh test đơn giản
    height, width = 720, 1280
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Tạo background gradient đẹp
    for y in range(height):
        for x in range(width):
            # Gradient từ xanh dương đến cam
            ratio_y = y / height
            blue = int(135 * (1 - ratio_y))
            green = int(206 * (1 - ratio_y) + 165 * ratio_y)
            red = int(235 * ratio_y + 100 * (1 - ratio_y))
            image[y, x] = [blue, green, red]
    
    try:
        # Test thêm text
        result_image = add_morning_vibes_text(image.copy())
        
        # Lưu ảnh test
        cv2.imwrite("quick_test_result.jpg", result_image)
        print("✅ Test thành công! Đã tạo quick_test_result.jpg")
        print("📏 Kích thước: 1280x720")
        print("✨ Chữ 'Morning Vibes' đã được thêm với font to hơn")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test()
